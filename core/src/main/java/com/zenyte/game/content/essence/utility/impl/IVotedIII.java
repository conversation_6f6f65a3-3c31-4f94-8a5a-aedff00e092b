package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

public class IVotedIII extends E<PERSON><PERSON>Perk {

    public static int CHANCE = 75;
    public static boolean roll() {
        return Utils.percentage(CHANCE);
    }

    @Override
    public String name() {
        return "I Voted (III)";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_IVotedIII;
    }

    @Override
    public String description() {
        return "Provides a " + CHANCE + "% chance to double your vote rewards when claiming.";
    }

    @Override
    public int item() {
        return 2996;
    }

    @Override
    public boolean purchasable(Player p) {
        return p.hasUtilityPerk(IVotedII.class);
    }

    @Override
    public String purchasableErrorMessage(Player player) {
        return "You must unlock the I Voted (II) perk before you can purchase the I Voted (III) perk.";
    }
}
