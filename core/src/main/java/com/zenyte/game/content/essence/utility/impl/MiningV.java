package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> (Discord: Kryptexxical)
 */
public class MiningV extends EssencePerk {

    public static boolean roll() {
        return true;
    }
    public static boolean rollBank() { return Utils.random(99) < 75; }
    @Override
    public int price() {
        return EssencePerkPriceTable.v_MiningV;
    }

    @Override
    public String name() {
        return "Mining (V)";
    }

    @Override
    public String description() {
        return "Increases your double ore chance while mining to 100%, in addition to your 75% chance of banking ores.";
    }

    @Override
    public boolean purchasable(Player p) {
        return p.hasUtilityPerk(MiningIV.class);
    }

    @Override
    public String purchasableErrorMessage(Player player) {
        return "You must unlock the Mining (IV) perk before you can purchase the Mining (V) perk.";
    }
    @Override
    public int item() {
        return 451;
    }

}
