package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> (Discord: Kryptexxical)
 */
public class WoodcuttingV extends EssencePerk {

    public static boolean roll() {
        return true;
    }
    public static boolean rollBank() { return Utils.random(99) < 75; }
    @Override
    public int price() {
        return EssencePerkPriceTable.v_WoodcuttingV;
    }

    @Override
    public String name() {
        return "Woodcutting (V)";
    }

    @Override
    public String description() {
        return "Increases your double log chance while Woodcutting to 100%, in addition to your 75% chance of banking logs.";
    }

    @Override
    public boolean purchasable(Player p) {
        return p.hasUtilityPerk(WoodcuttingIV.class);
    }

    @Override
    public String purchasableErrorMessage(Player player) {
        return "You must unlock the Woodcutting (IV) perk before you can purchase the Woodcutting (V) perk.";
    }
    @Override
    public int item() {
        return 1511;
    }

}
