package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class FishingI extends EssencePerk {

    public static boolean roll() {
        return Utils.random(99) < 20;
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_FishingI;
    }

    @Override
    public String name() {
        return "Fishing (I)";
    }

    @Override
    public String description() {
        return "20% chance to catch double fish whilst fishing.";
    }

    @Override
    public int item() {
        return 317;
    }

}
