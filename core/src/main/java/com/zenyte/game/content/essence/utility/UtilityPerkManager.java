package com.zenyte.game.content.essence.utility;

import com.near_reality.game.content.shop.ShopCurrencyHandler;
import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.utility.impl.UnknownEssencePerk;
import com.zenyte.game.model.shop.ShopCurrency;
import com.zenyte.game.util.Colour;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.region.area.DuelArenaArea;

import java.util.ArrayList;

/**
 * This manager class interfaces with Player Perks that are unlocked with sacrifice/exchange points
 * to provide various bonuses in all elements of the game
 */
public class UtilityPerkManager {
    public ArrayList<String> unlockedPerks = new ArrayList<>();
    public ArrayList<String> toggleOffPerks = new ArrayList<>();
    private transient final Player player;

    public UtilityPerkManager(Player player) {
        this.player = player;
    }

    public void forceUnlock(String perk) {
        if(!unlockedPerks.contains(perk)) {
            player.notification("Utility Perk Manager", "Perk Unlocked:<br><br>" + Colour.WHITE.wrap(perk), 16750623);
            unlockedPerks.add(perk);
        }
    }

    public boolean hasPerk(Class<? extends EssencePerk> lookupPerk) {
        EssencePerk essencePerk = UtilityPerkLoader.findPerk(lookupPerk);

        if(player.getArea() != null && player.getArea() instanceof DuelArenaArea)
            return false;

        if(player.inCombatWithPlayer())
            return false;

        return essencePerk.isActive(player) && (essencePerk.isAlwaysUnlocked(player) || unlockedPerks.contains(essencePerk.getClass().getSimpleName()) || unlockedPerks.contains(essencePerk.getAlternateName())) && !toggleOffPerks.contains(essencePerk.getClass().getSimpleName());
    }

    public boolean purchasePerk(Class<?> lookupPerk, int index) {
        EssencePerk essencePerk = UtilityPerkLoader.findPerk(lookupPerk);
        if(essencePerk instanceof UnknownEssencePerk) {
            player.sendDeveloperMessage("Unknown error attempting to purchase this perk.");
            return false;
        }
        if(unlockedPerks.contains(essencePerk.getClass().getSimpleName())) {
            player.sendMessage("You already have this perk unlocked.");
            return false;
        }
        if (!essencePerk.purchasable(player)) {
            player.sendMessage("<col=ff0000>" + essencePerk.purchasableErrorMessage(player) + "</col>");
            return false;
        }
        int available = ShopCurrencyHandler.getAmount(ShopCurrency.UTILITY_ESSENCE, player);
        if(essencePerk.price() <= available) {
            ShopCurrencyHandler.remove(ShopCurrency.UTILITY_ESSENCE, player, essencePerk.price());
            unlockedPerks.add(essencePerk.getClass().getSimpleName());
            player.notification("Utility Perks Manager", "Utility Perk Unlocked:<br><br>" + Colour.WHITE.wrap(essencePerk.name()), 16750623);
            player.sendMessage("You have unlocked " + Colour.BLUE.wrap(essencePerk.name()) + " for " + essencePerk.price() + " Utility Essence.");
            player.getVarManager().sendVarInstant(4506, ShopCurrencyHandler.getAmount(ShopCurrency.UTILITY_ESSENCE, player));
            player.getVarManager().sendBitInstant(19499 + index, 1);
            player.getVarManager().sendBitInstant(19498, 1);
            player.getPacketDispatcher().sendClientScript(12586, 998, essencePerk.name());
            return true;
        } else {
            player.sendMessage("You do not have enough Utility Essence.");
            return false;
        }
    }

    public void togglePerk(Class<?> lookupPerk) {
        EssencePerk essencePerk = UtilityPerkLoader.findPerk(lookupPerk);
        String perkName = essencePerk.getClass().getSimpleName();
        if(essencePerk instanceof UnknownEssencePerk) {
            player.sendDeveloperMessage("Unknown error attempting to toggle this perk.");
            return;
        }
        if(!unlockedPerks.contains(perkName)) {
            player.sendMessage("You need to purchase this perk first");
            return;
        }
        if(toggleOffPerks.contains(perkName)) {
            toggleOffPerks.remove(perkName);
            player.sendMessage(Colour.RS_GREEN.wrap(essencePerk.name() + " has been toggled ON!"));
        } else {
            toggleOffPerks.add(perkName);
            player.sendMessage(Colour.RS_RED.wrap(essencePerk.name() + " has been toggled OFF!"));
        }
    }


    public void initialize(UtilityPerkManager manager) {
        if (manager != null && manager.unlockedPerks != null) {
            unlockedPerks = manager.unlockedPerks;
        }

        if (manager != null && manager.toggleOffPerks != null) {
            toggleOffPerks = manager.toggleOffPerks;
        }
    }
}
