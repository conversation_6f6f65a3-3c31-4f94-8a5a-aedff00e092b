package com.zenyte.game.content.essence.utility;

import com.google.common.eventbus.Subscribe;
import com.zenyte.game.content.essence.DisabledEssencePerk;
import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.utility.impl.UnknownEssencePerk;
import com.zenyte.plugins.events.ServerLaunchEvent;
import com.zenyte.utils.ClassInitializer;
import io.github.classgraph.ClassGraph;
import io.github.classgraph.ScanResult;
import it.unimi.dsi.fastutil.objects.ObjectArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.concurrent.Callable;

import static java.util.concurrent.ForkJoinPool.commonPool;

@SuppressWarnings("unused")
public final class UtilityPerkLoader {

    @Subscribe
    public static void onServerLaunch(final ServerLaunchEvent event) {
        new UtilityPerkLoader().init();
    }

    private static final Logger log = LoggerFactory.getLogger(UtilityPerkLoader.class);

    public static ArrayList<EssencePerk> essencePerkTypes = new ArrayList<>();

    public static EssencePerk findPerk(Class<?> lookupPerk) {
        return essencePerkTypes.stream().filter(it -> it.getClass() == lookupPerk).findAny().orElse(new UnknownEssencePerk());
    }

    public void init() {
        final ClassGraph scanner = new ClassGraph();
        scanner.ignoreMethodVisibility();
        scanner.enableAnnotationInfo();
        scanner.enableMethodInfo();
        scanner.enableClassInfo();
        scanner.enableExternalClasses();
        scanner.acceptPackages("com.zenyte.game.content.essence.utility.impl");


        log.debug("Scanning for utility perks in classpath.");
        try (ScanResult result = scanner.scan()) {
            final ObjectArrayList<Callable<Void>> callables = new ObjectArrayList<>(1000);
            final Object lock = new Object();
            result.getAllClasses().forEach(clazz -> {
                callables.add(() -> {
                    if (clazz.extendsSuperclass(EssencePerk.class) && !clazz.hasAnnotation(DisabledEssencePerk.class)) {
                        log.trace("Loading utility perks: " + clazz.getSimpleName());
                        ClassInitializer.initialize(clazz.loadClass());
                        final Class<?> obj = clazz.loadClass();
                        synchronized (lock) {
                            EssencePerk essencePerk = (EssencePerk) obj.getDeclaredConstructor().newInstance();
                            essencePerkTypes.add(essencePerk);
                        }

                    }
                    return null;
                });
            });
            log.debug("Processing utility perks result.");
            commonPool().invokeAll(callables);
            //essencePerkTypes.sort(Comparator.comparingInt(EssencePerk::price).thenComparing(EssencePerk::name));
            log.info("Loaded a total of {} utility perks from the classpath", essencePerkTypes.size());
        }
    }
}
