package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class WoodcuttingI extends EssencePerk {

    public static boolean roll() {
        return Utils.random(99) < 20;
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_WoodcuttingI;
    }

    @Override
    public String name() {
        return "Woodcutting (I)";
    }

    @Override
    public String description() {
        return "20% chance to chop double logs from woodcutting.";
    }

    @Override
    public int item() {
        return 1511;
    }

}
