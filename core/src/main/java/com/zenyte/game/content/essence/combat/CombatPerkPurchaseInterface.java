package com.zenyte.game.content.essence.combat;

import com.near_reality.game.content.shop.ShopCurrencyHandler;
import com.zenyte.game.GameInterface;
import com.zenyte.game.model.shop.ShopCurrency;
import com.zenyte.game.model.ui.Interface;
import com.zenyte.game.model.ui.InterfacePosition;
import com.zenyte.game.util.AccessMask;
import com.zenyte.game.world.entity.player.Player;

import static com.zenyte.game.GameInterface.COMBAT_PERK_SHOP;

public class CombatPerkPurchaseInterface extends Interface {
    @Override
    protected void attach() {
        put(8, "Buy");
        put(11, "Entry");
        put(13, "Open Overview");
        put(20, "Sacrifice Items");
    }

    @Override
    public void open(Player player) {
        player.getVarManager().sendVarInstant(4508, ShopCurrencyHandler.getAmount(ShopCurrency.COMBAT_ESSENCE, player));
        updateAllUnlockedPerks(player);
        player.getPacketDispatcher().sendComponentSettings(getInterface(), 8, 0, 10, AccessMask.CLICK_OP1, AccessMask.CLICK_OP2, AccessMask.CLICK_OP3, AccessMask.CLICK_OP5);
        player.getPacketDispatcher().sendComponentSettings(getInterface(), 11, 0, CombatPerkLoader.essencePerkTypes.size() * 5, AccessMask.CLICK_OP3, AccessMask.CLICK_OP5);
        player.getInterfaceHandler().sendInterface(InterfacePosition.CENTRAL, getInterface().getId());
    }

    private void updateAllUnlockedPerks(Player player) {
        for(CombatPerkWrapper wrapper: CombatPerkWrapper.values()) {
            if(player.getCombatPerkManager().hasPerk(wrapper.getPerk()) && wrapper.getId() != -1) {
                player.getVarManager().sendBitInstant(19699 + wrapper.getId(), 1);
            }
        }
    }

    @Override
    protected void build() {
        bind("Buy", (player, slotId, itemId, option) -> {
            player.getCombatPerkManager().purchasePerk(CombatPerkWrapper.getByClass(itemId).getPerk(), itemId);
        });
        bind("Entry", (player, slotId, itemId, option) -> player.getCombatPerkManager().togglePerk(CombatPerkWrapper.getByClass((slotId / 5) + 1).getPerk()));
        bind("Open Overview", (player, slotId, itemId, option) -> GameInterface.PERK_OVERVIEW.open(player));
        bind("Sacrifice Items", GameInterface.SACRIFICE_ESSENCE::open);
    }

    @Override
    public GameInterface getInterface() {
        return COMBAT_PERK_SHOP;
    }
}
