package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class MiningI extends EssencePerk {

    public static boolean roll() {
        return Utils.random(99) < 20;
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_MiningI;
    }

    @Override
    public String name() {
        return "Mining I";
    }

    @Override
    public String description() {
        return "20% chance to mine double ore.";
    }

    @Override
    public int item() {
        return 436;
    }

}
