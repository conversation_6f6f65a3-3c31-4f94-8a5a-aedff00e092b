package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> (Discord: Kryptexxical)
 */
public class FishingIV extends EssencePerk {

    public static boolean roll() {
        return Utils.random(99) < 50;
    }
    public static boolean rollBank() { return Utils.random(99) < 75; }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_FishingIV;
    }

    @Override
    public String name() {
        return "Fishing (IV)";
    }

    @Override
    public String description() {
        return "Increases your chance of banking fishs while Fishing to 75%, in addition to your 50% chance of Double fish.";
    }

    @Override
    public boolean purchasable(Player p) {
        return p.hasUtilityPerk(FishingIII.class);
    }

    @Override
    public String purchasableErrorMessage(Player player) {
        return "You must unlock the Fishing (III) perk before you can purchase the Fishing (IV) perk.";
    }

    @Override
    public int item() {
        return 383;
    }

}
