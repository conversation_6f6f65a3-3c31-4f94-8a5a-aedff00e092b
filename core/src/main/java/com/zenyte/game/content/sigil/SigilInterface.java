package com.zenyte.game.content.sigil;

import com.zenyte.game.GameInterface;
import com.zenyte.game.content.quests.QuestManager;
import com.zenyte.game.content.quests.quests.AdventurersPath;
import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.model.ui.Interface;
import com.zenyte.game.util.Colour;
import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> (Disord: astra4).
 */
public class SigilInterface extends Interface {

	/**
	 * CS2: 5230, 5232, 5231, 5234
	 * Enums: 4039 (Attuned Sigils), 4040 (Unattuned Sigils), 4041 (Type)
	 * VARBIT: 13019, 13020, 13021 -> Active sigils
	 * VARP: 261, 262, 263 -> Select a free sigil
	 */

	public static final int FIRST_SIGIL_VARPBIT = 13019;
	public static final int SECOND_SIGIL_VARPBIT = 13020;
	public static final int THIRD_SIGIL_VARPBIT = 13021;

	@Override protected void attach() {
		put(6, "Free Sigil Resistance");
		put(7, "Free Sigil Deft Strikes");
		put(8, "Free Sigil Consistency");
	}

	@Override public void open(Player player) {
		player.getVarManager().sendVar(261, player.getBooleanAttribute("free_sigil_claimed") ? 0 : 1);
		player.getVarManager().sendVar(262, player.getBooleanAttribute("free_sigil_claimed") ? 0 : 2);
		player.getVarManager().sendVar(263, player.getBooleanAttribute("free_sigil_claimed") ? 0 : 3);
		player.getSigilManager().refreshActiveSigilInterface();
		super.open(player);
	}

	@Override protected void build() {
		bindFreeSigil("Free Sigil Resistance", new Item(ItemId.SIGIL_OF_RESISTANCE_28490));
		bindFreeSigil("Free Sigil Deft Strikes", new Item(ItemId.SIGIL_OF_DEFT_STRIKES_26012));
		bindFreeSigil("Free Sigil Consistency", new Item(ItemId.SIGIL_OF_CONSISTENCY_25994));
	}

	@Override public GameInterface getInterface() {
		return GameInterface.SIGILS;
	}

	private void bindFreeSigil(String optionName, Item item) {
		bind(optionName, (player, slotId, clickedItemId, option) -> {
			if (AdventurersPath.isOnObjectiveWithProgress(player, AdventurersPath.CLAIM_FREE_SIGIL, 0)) {
				player.getInventory().addItem(item);
				player.sendMessage("You've selected " + item.getName() + " as your free sigil! It can last for 6 hours while attuned, and then it will crumble to dust.");
				player.sendMessage("You can type <col=000fff>::sigils</col> or click <col=000fff>Sigil Manager</col> in the Dashboard at any time to open the Attuned Sigils manager.");
				player.putBooleanAttribute("free_sigil_claimed", true);
				close(player);
				player.setGraphics(Sigil.attuneGfx);
				player.notification("Sigil Manager", "You've claimed your free Sigil!", 16750623);
				QuestManager.updateQuest(player, AdventurersPath.class, "CLAIM_FREE_SIGIL");
				player.sendMessage("<col=ff0000>You've claimed your free Sigil! Please return to the Exiles Guide.</col>");
				if (player.getTemporaryAttributes().get("last hint arrow") != null) {
					player.getPacketDispatcher().resetHintArrow();
				}
			} else {
				player.sendMessage(Colour.BRICK.wrap("You must claim your free Sigil during the Adventurer's Path quest. Talk to the Exiles Guide to get started!"));
			}
		});
	}
}
