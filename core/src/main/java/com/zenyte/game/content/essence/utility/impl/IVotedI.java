package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;

public class IVotedI extends EssencePerk {

    public static int CHANCE = 25;
    public static boolean roll() {
        return Utils.percentage(CHANCE);
    }

    @Override
    public String name() {
        return "I Voted (I)";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_IVotedI;
    }

    @Override
    public String description() {
        return "Provides a " + CHANCE + "% chance to double your vote rewards when claiming.";
    }

    @Override
    public int item() {
        return 2996;
    }
}
