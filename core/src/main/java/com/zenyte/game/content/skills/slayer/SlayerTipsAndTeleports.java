package com.zenyte.game.content.skills.slayer;

import com.zenyte.game.content.skills.magic.spells.teleports.Teleport;
import com.zenyte.game.content.skills.magic.spells.teleports.TeleportType;
import com.zenyte.game.item.Item;
import com.zenyte.game.util.Colour;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.privilege.MemberRank;
import com.zenyte.game.world.entity.player.privilege.PlayerPrivilege;
import com.zenyte.plugins.dialogue.OptionsMenuD;

import java.util.ArrayList;
import java.util.List;

/**
 * Handles slayer tips and teleports for players using a unified interface.
 * All players see the same interface with task tips and red location names.
 * Donators can click location names to teleport, non-donators get a message about needing donator status.
 *
 * <AUTHOR> - (Discord: imslickk)
 */
public class SlayerTipsAndTeleports {

    /**
     * Shows the unified slayer tips and teleports interface.
     * All players see the same interface with task tips and red location names.
     * Donators can click location names to teleport, non-donators get a donator message.
     *
     * @param player The player requesting the interface
     */
    public static void showSlayerTips(Player player) {
        showSlayerTipsAndTeleports(player);
    }

    /**
     * Shows the unified slayer tips and teleports interface.
     * All players see the same OptionsMenuD interface with task tips and red location names.
     * Donators can click location names to teleport.
     * Non-donators get a message about needing donator status when clicking locations.
     *
     * @param player The player requesting the interface
     */
    public static void showSlayerTipsAndTeleports(Player player) {
        Assignment assignment = player.getSlayer().getAssignment();
        if (assignment == null) {
            player.sendMessage("You don't have a slayer task assigned.");
            return;
        }

        SlayerTask task = assignment.getTask();
        List<LocationInfo> locations = getTaskLocations(task);

        if (locations.isEmpty()) {
            player.sendMessage("No location information is available for your current task: " + task.toString());
            return;
        }
        showAllLocationsInterface(player, task, locations);
    }

    /**
     * Shows the unified OptionsMenuD interface for all players.
     * Interface shows task tip first (non-clickable), then red location names.
     * All players see the same visual interface.
     * Donators can click location names to teleport.
     * Non-donators get a donator message when clicking location names.
     *
     * @param player The player
     * @param task The slayer task
     * @param locations List of all locations
     */
    private static void showAllLocationsInterface(Player player, SlayerTask task, List<LocationInfo> locations) {
        boolean isDonator = player.getMemberRank().equalToOrGreaterThan(MemberRank.REGULAR);

        String[] menuOptions = locations.stream()
                .map(LocationInfo::getLocationName)
                .toArray(String[]::new);

        player.sendMessage(Colour.BRICK.wrap("Slayer Tip</col>: " + task.getTip()));

        player.getDialogueManager().start(new OptionsMenuD(player,
                "Slayer Teleports - " + task, menuOptions) {
            @Override
            public void handleClick(int slotId) {
                if (slotId < 0 || slotId >= locations.size()) {
                    return;
                }

                LocationInfo location = locations.get(slotId);
                if (isDonator || player.getPrivilege().inherits(PlayerPrivilege.SUPPORT)) {
                    handleTeleport(player, task, location);
                } else {
                    player.sendMessage("You need to be a donator to teleport to slayer locations.");
                }
            }
        });
    }

    /**
     * Handles teleportation to a specific location.
     *
     * @param player The player
     * @param task The slayer task
     * @param location The location to teleport to
     */
    private static void handleTeleport(Player player, SlayerTask task, LocationInfo location) {
        Location teleportLoc = location.getTeleportLocation();
        if (teleportLoc.getX() == -1 && teleportLoc.getY() == -1) {
            player.sendMessage("Teleport is not setup for this slayer monster.");
            player.sendMessage("Location: " + location.getLocationName() + " (" + task.toString() + ")");
            return;
        }
        new SlayerTaskTeleport(teleportLoc).teleport(player);
        player.sendMessage("Teleported to " + location.getLocationName() + " for your slayer task.");
    }

    /**
     * Gets all location information for a given slayer task.
     *
     * @param task The slayer task
     * @return List of location information
     */
    private static List<LocationInfo> getTaskLocations(SlayerTask task) {
        List<LocationInfo> locations = new ArrayList<>();

        if (task instanceof RegularTask) {
            RegularTask regularTask = (RegularTask) task;
            List<RegularTask.SlayerLocationInfo> taskLocations = regularTask.getLocations();
            for (RegularTask.SlayerLocationInfo loc : taskLocations) {
                locations.add(new LocationInfo(loc.getLocationName(), loc.getTeleportLocation()));
            }
        } else if (task instanceof BossTaskSumona) {
            BossTaskSumona bossTask = (BossTaskSumona) task;
            List<BossTaskSumona.SlayerLocationInfo> taskLocations = bossTask.getLocations();
            for (BossTaskSumona.SlayerLocationInfo loc : taskLocations) {
                locations.add(new LocationInfo(loc.getLocationName(), loc.getTeleportLocation()));
            }
        }

        return locations;
    }



    /**
     * Teleport class for slayer task locations.
     */
    public static class SlayerTaskTeleport implements Teleport {
        private final Location destination;

        public SlayerTaskTeleport(Location destination) {
            this.destination = destination;
        }

        @Override
        public TeleportType getType() {
            return TeleportType.REGULAR_TELEPORT;
        }

        @Override
        public Location getDestination() {
            return destination;
        }

        @Override
        public int getLevel() {
            return 0;
        }

        @Override
        public double getExperience() {
            return 0;
        }

        @Override
        public int getRandomizationDistance() {
            return 0;
        }

        @Override
        public Item[] getRunes() {
            return null;
        }

        @Override
        public int getWildernessLevel() {
            return WILDERNESS_LEVEL;
        }

        @Override
        public boolean isCombatRestricted() {
            return UNRESTRICTED;
        }
    }

    /**
     * Unified location information class.
     */
    private static class LocationInfo {
        private final String locationName;
        private final Location teleportLocation;

        public LocationInfo(String locationName, Location teleportLocation) {
            this.locationName = locationName;
            this.teleportLocation = teleportLocation;
        }

        public String getLocationName() { return locationName; }
        public Location getTeleportLocation() { return teleportLocation; }
    }
}
