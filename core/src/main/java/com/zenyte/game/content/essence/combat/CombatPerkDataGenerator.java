package com.zenyte.game.content.essence.combat;

import com.google.gson.Gson;
import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.*;
import com.zenyte.game.content.essence.combat.exchange.CombatEssenceValue;
import com.zenyte.game.content.essence.combat.exchange.CombatEssenceValueManager;
import com.zenyte.game.world.DefaultGson;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.util.ArrayList;

public class CombatPerkDataGenerator {
    static Gson gsonInstance;

    static ArrayList<CombatPerkModel> output = new ArrayList<>();
    static ArrayList<CombatEssenceValueModel> output2 = new ArrayList<>();

    public static void main(String[] args) throws IOException {
        CombatPerkLoader loader = new CombatPerkLoader();
        gsonInstance = DefaultGson.getGson();
        loader.init();
        for(EssencePerk essencePerk : CombatPerkLoader.essencePerkTypes) {
            if (essencePerk.isHidden())
                continue;
            output.add(new CombatPerkModel(essencePerk.name(), essencePerk.description(), essencePerk.price(), essencePerk.sprite(), CombatPerkWrapper.getByClass(essencePerk.getClass()).getId()));
        }
        File file;
        if((file = new File("./cache/data/dynamic/combat_perk_data.json")).exists()) {
            file.delete();
        }
        Writer writer = new FileWriter("./cache/data/dynamic/combat_perk_data.json");
        gsonInstance.toJson(output, writer);
        writer.close();

        CombatEssenceValueManager.categorize();

        for(CombatEssenceValue value: CombatEssenceValueManager.values) {
            output2.add(new CombatEssenceValueModel(value.getValue(), value.getIds()));
        }

        File file2;
        if((file2 = new File("./cache/data/dynamic/combat_essence_data.json")).exists()) {
            file2.delete();
        }
        Writer writer2 = new FileWriter("./cache/data/dynamic/combat_essence_data.json");
        gsonInstance.toJson(output2, writer2);
        writer2.close();

    }
}
