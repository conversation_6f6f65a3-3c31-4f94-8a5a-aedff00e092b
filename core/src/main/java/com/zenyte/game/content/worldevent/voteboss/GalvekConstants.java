package com.zenyte.game.content.worldevent.voteboss;

import com.zenyte.game.world.Projectile;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.masks.Animation;
import com.zenyte.game.world.entity.npc.NpcId;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class GalvekConstants {


    public static final int BOOST_HOURS = 2;
    public static final int AMT_TO_SPAWN = 50;
    public static final long GALVEK_SPAWN_DELAY_IN_SECONDS = 60L;

    public static final int GALVEK_NORMAL = NpcId.GALVEK;
    public static final int GALVEK_MAGIC = NpcId.GALVEK_8096;
    public static final int GALVEK_MELEE = NpcId.GALVEK_8097;
    public static final int GALVEK_RANGED = NpcId.GALVEK_8098;
    public static final int GALVEK_SLEEPING = NpcId.GALVEK_8179;

    public static final Location SPAWN_LOCATION = new Location(1628, 5724, 2);

    public static final Animation SLEEPING_ANIM = new Animation(7913);
    public static final Animation SPAWN_ANIM = new Animation(7907);
    public static final Animation LAND_ANIM = new Animation(7908);
    public static final Animation DISAPPEAR_ANIM = new Animation(7909);
    public static final Animation STAND_ANIM = new Animation(7902);
    public static final Animation MAGIC_ANIM = new Animation(7904);
    public static final Animation MELEE_ANIM = new Animation(7900);
    public static final Animation RANGED_ANIM = new Animation(7914);
    public static final Animation CHANGE_ANIM = new Animation(7910);
    public static final Animation DEATH_ANIM = new Animation(7915);

    public static final Projectile RANGED_PROJ = new Projectile(1293, 70, 15, 60, 15, 10, 64, 5);
    public static final Projectile MAGIC_PROJ = new Projectile(449, 70, 15, 60, 15, 10, 64, 5);


}
