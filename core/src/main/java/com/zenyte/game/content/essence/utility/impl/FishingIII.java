package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> (Discord: Kryptexxical)
 */
public class FishingIII extends EssencePerk {

    public static boolean roll() {
        return Utils.random(99) < 50;
    }
    public static boolean rollBank() { return Utils.random(99) < 30; }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_FishingIII;
    }

    @Override
    public String name() {
        return "Fishing (III)";
    }

    @Override
    public String description() {
        return "Increases your double fish chance while Fishing to 50%, in addition to your 30% chance to bank resources.";
    }
    @Override
    public boolean purchasable(Player p) {
        return p.hasUtilityPerk(FishingII.class);
    }

    @Override
    public String purchasableErrorMessage(Player player) {
        return "You must unlock the Fishing (II) perk before you can purchase the Fishing (III) perk.";
    }
    @Override
    public int item() {
        return 7944;
    }

}
