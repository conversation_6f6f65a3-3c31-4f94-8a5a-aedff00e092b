package com.zenyte.game.content.essence.combat;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.combat.impl.*;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;

import java.util.HashMap;
import java.util.Map;

public enum CombatPerkWrapper {
    /*
     * IMPORTANT: Add new perks to the bottom of the list (before disabled ones)
     * If you don't, currently unlocked perk ids will change and everything will be fucked
     */

    MeleeISpeed(1, MeleeISpeed.class),
    MeleeIIAccuracy(2, MeleeIIAccuracy.class),
    MeleeIIIDamage(3, MeleeIIIDamage.class),
    RangedISpeed(4, RangedISpeed.class),
    RangedIIAccuracy(5, RangedIIAccuracy.class),
    RangedIIIDamage(6, RangedIIIDamage.class),
    MagicISpeed(7, MagicISpeed.class),
    MagicIIAccuracy(8, MagicIIAccuracy.class),
    MagicIIIDamage(9, MagicIIIDamage.class),
    RuneSaver(10, RuneSaver.class),
    AmmoS<PERSON>(11, AmmoSaver.class),
    <PERSON>eeL<PERSON>Leech(12, MeleeLifeLeech.class),
    RangedLifeLeech(13, RangedLifeLeech.class),
    MagicLifeLeech(14, MagicLifeLeech.class),
    StabWeaponryMaster(15, StabWeaponryMaster.class),
    SlashWeaponryMaster(16, SlashWeaponryMaster.class),
    CrushWeaponryMaster(17, CrushWeaponryMaster.class),
    SpecialRegen(18, SpecialRegen.class),
    SoulStealer(19, SoulStealer.class),
    Recoiled(20, Recoiled.class),
    TasteMoreVengeance(21, TasteMoreVengeance.class),
    Berserker(22, Berserker.class),
    MinionsMight(23, MinionsMight.class),
    HolyInterventionI(24, HolyInterventionI.class),
    HolyInterventionII(25, HolyInterventionII.class),
    HolyInterventionIII(26, HolyInterventionIII.class),
    SoulTiedTwistedBow(27, SoulTiedTwistedBow.class),
    SoulTiedScytheOfVitur(28, SoulTiedScytheOfVitur.class),
    SoulTiedTumekensShadow(29, SoulTiedTumekensShadow.class),

    /*
     * Add new perks above here at the end of the list.
     */

    Unknown(9999, UnknownEssencePerk.class),
    ;

    private final int id;
    private final Class<? extends EssencePerk> perk;
    private static final CombatPerkWrapper[] VALUES = values();
    public static final Map<Class<? extends EssencePerk>, CombatPerkWrapper> PERKS_BY_CLASS = new HashMap<>();
    private static final Map<String, CombatPerkWrapper> PERKS_BY_NAME = new HashMap<>();
    public static final Int2ObjectOpenHashMap<CombatPerkWrapper> PERKS_BY_ID = new Int2ObjectOpenHashMap<>();

    static {
        for (final CombatPerkWrapper value : VALUES) {
            PERKS_BY_CLASS.put(value.getPerk(), value);
            PERKS_BY_NAME.put(value.name(), value);
            PERKS_BY_ID.put(value.getId(), value);
        }
    }

    static {
        Map<Integer, CombatPerkWrapper> seen = new HashMap<>();
        for (CombatPerkWrapper perk : values()) {
            if (seen.containsKey(perk.id)) {
                throw new IllegalStateException(
                        "Duplicate CombatPerkWrapper ID " + perk.id +
                                " for " + perk.name() + " and " + seen.get(perk.id).name()
                );
            }
            seen.put(perk.id, perk);
        }
    }

    public static CombatPerkWrapper getByClass(final Class<? extends EssencePerk> perk) {
        if(!PERKS_BY_CLASS.containsKey(perk))
            throw new RuntimeException("MISSING BOON WRAPPER: " + perk.getSimpleName());
        return PERKS_BY_CLASS.get(perk);
    }

    public static CombatPerkWrapper getByString(final String perk) {
        return PERKS_BY_NAME.get(perk);
    }

    public static CombatPerkWrapper getByClass(final int id) {
        return PERKS_BY_ID.getOrDefault(id, Unknown);
    }

    CombatPerkWrapper(int id, Class<? extends EssencePerk> perk) {
        this.id = id;
        this.perk = perk;
    }

    public int getId() {
        return id;
    }

    public Class<? extends EssencePerk> getPerk() {
        return perk;
    }

}
