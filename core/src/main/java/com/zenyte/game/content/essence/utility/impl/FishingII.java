package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> (Discord: Kryptexxical)
 */
public class FishingII extends EssencePerk {

    public static boolean roll() {
        return Utils.random(99) < 20;
    }
    public static boolean rollBank() { return Utils.random(99) < 30; }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_FishingII;
    }

    @Override
    public String name() {
        return "Fishing (II)";
    }

    @Override
    public String description() {
        return "Adds a 30% chance to bank your fish while Fishing in addition to your 20% chance to double fish.";
    }

    @Override
    public boolean purchasable(Player p) {
        return p.hasUtilityPerk(FishingI.class);
    }

    @Override
    public String purchasableErrorMessage(Player player) {
        return "You must unlock the Fishing (I) perk before you can purchase the Fishing (II) perk.";
    }

    @Override
    public int item() {
        return 377;
    }

}
