package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;

public class ClueStepMinimizer extends <PERSON>ssencePerk {
    @Override
    public String name() {
        return "Clue Step Minimizer";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_ClueStepMinimizer;
    }

    @Override
    public String description() {
        return "Easy & Medium become instant solve, Hard & Elite clues will become 1 step. Master clues will become 2 steps.";
    }

    @Override
    public int item() {
        return 2575;
    }
}
