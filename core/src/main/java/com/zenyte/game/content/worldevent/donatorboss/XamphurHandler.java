package com.zenyte.game.content.worldevent.donatorboss;

import com.google.common.eventbus.Subscribe;
import com.near_reality.tools.discord.community.DiscordBroadcastKt;
import com.near_reality.tools.discord.community.DiscordCommunityBot;
import com.zenyte.game.content.worldboost.type.WorldBossBoost;
import com.zenyte.game.task.WorldTask;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.content.worldboost.WorldBoost;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.World;
import com.zenyte.game.world.broadcasts.BroadcastType;
import com.zenyte.game.world.broadcasts.WorldBroadcasts;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.plugins.events.ServerLaunchEvent;
import com.zenyte.utils.TimeUnit;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class XamphurHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(XamphurHandler.class);

    private WorldTask xamphurSpawnTask;

    private int donations;

    private boolean enabled = true;

    private final Xamphur xamphur = new Xamphur();

    public static XamphurHandler get() {
        return instance;
    }

    private int announcements;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public void addDonations(Player player, int amt) {
        if(!enabled)
            return;

        donations +=amt;

        if(donations >= XamphurConstants.AMT_TO_SPAWN) {
            donations = 0;
            announcements = 0;
            if (xamphurSpawnTask != null) {
                LOGGER.warn("Cancelling previous xamphur spawn task in favor of new one.");
                WorldTasksManager.stop(xamphurSpawnTask);
            }
            xamphurSpawnTask = () -> {
                WorldBroadcasts.sendMessage("<img=22> The Donator Boss has spawned at ::db", BroadcastType.DONATOR_BOSS, true);
                xamphur.start();
            };
            WorldBroadcasts.sendMessage("<img=22> The Donator Boss will spawn in 60 seconds at ::db", BroadcastType.DONATOR_BOSS, true);
            WorldTasksManager.schedule(xamphurSpawnTask, (int) TimeUnit.SECONDS.toTicks(XamphurConstants.XAMPHUR_SPAWN_DELAY_IN_SECONDS));
        } else {

            if(donations >= 99 && announcements == 0 || donations >= 199 && announcements == 1) {
                int announcedAmount = switch (announcements) {
                    case 0 -> 100;
                    case 1 -> 200;
                    default -> 0;
                };
                WorldBroadcasts.sendMessage("<img=" + 22 + "><col=" + "e59400" + ">" + "<shad=000000>" + "Event: $" + announcedAmount + " donated until the Donator Boss spawns!", BroadcastType.DONATOR_BOSS, false);
                DiscordBroadcastKt.onVotesMilestone(DiscordCommunityBot.INSTANCE, player, amt, amtTillSpawn());
                announcements++;
            }
        }
    }

    public Xamphur getXamphur() {
        return xamphur;
    }

    public int amtTillSpawn() {
        return XamphurConstants.AMT_TO_SPAWN - donations;
    }


    private static XamphurHandler instance;


    public void activateRandomInactiveWorldBoost() {
        if(!enabled) {
            return;
        }

        List<WorldBossBoost> availableBoosts = new ArrayList<>(List.of(WorldBossBoost.VALUES));
        availableBoosts.removeIf(World::hasBoost);

        WorldBossBoost random = Utils.random(availableBoosts);
        if (random == null) {
            return;
        }

        activateBoost(random);
    }

    public static void activateBoost(WorldBossBoost boost) {
        activateBoost(boost, XamphurConstants.BOOST_HOURS);
    }

    public static void activateBoost(WorldBossBoost boost, int hours) {
        activateBoost(null, boost, hours);
    }

    public static void activateBoost(@Nullable Player activator, WorldBossBoost boost, int hours) {
        World.getWorldBoosts().stream()
                .filter(b -> b.getBoostType() == boost)
                .findAny()
                .ifPresentOrElse(
                        existingBoost -> existingBoost.extend(activator, 1, true),
                    () -> {
                        final long endTime = System.currentTimeMillis() + TimeUnit.HOURS.toMillis(hours);
                        WorldBoost worldBoost = new WorldBoost(boost, endTime, hours);
                        worldBoost.activate(activator, true);
                    }
                );
    }

    public static void activateBoost(WorldBossBoost boost, int hours, boolean announce) {
        long endTime = System.currentTimeMillis() + TimeUnit.HOURS.toMillis(hours);
        WorldBoost worldBoost = new WorldBoost(boost, endTime, hours);
        worldBoost.activate(announce);
    }

    private void start() {
        WorldTasksManager.schedule(() -> World.spawnNPC(xamphur));
    }

    @Subscribe
    public static void onServerLaunch(ServerLaunchEvent event) {
        instance = new XamphurHandler();
        instance.start();
    }

    @SuppressWarnings("ConstantValue")
    public boolean isXamphurActive() {
        return xamphur != null && xamphur.fightStarted;
    }
}
