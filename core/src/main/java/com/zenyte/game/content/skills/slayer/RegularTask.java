package com.zenyte.game.content.skills.slayer;

import com.zenyte.game.GameConstants;
import com.zenyte.game.content.boss.grotesqueguardians.boss.Dusk;
import com.zenyte.game.content.kebos.alchemicalhydra.npc.AlchemicalHydra;
import com.zenyte.game.content.kebos.konar.map.KaruulmSlayerDungeon;
import com.zenyte.game.content.minigame.fightcaves.FightCaves;
import com.zenyte.game.content.minigame.fightcaves.npcs.TzTokJad;
import com.zenyte.game.content.minigame.inferno.instance.Inferno;
import com.zenyte.game.content.minigame.inferno.npc.impl.zuk.TzKalZuk;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.npc.impl.slayer.superior.SuperiorNPC;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.Setting;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.region.GlobalAreaManager;
import com.zenyte.game.world.region.RegionArea;
import com.zenyte.game.world.region.area.*;
import com.zenyte.game.world.region.area.godwars.GodwarsDungeonArea;
import com.zenyte.game.world.region.area.kourend.*;
import com.zenyte.game.world.region.area.taskonlyareas.KalphiteCave;
import com.zenyte.game.world.region.area.taskonlyareas.KrakenCove;
import com.zenyte.game.world.region.area.taskonlyareas.StrongholdSlayerDungeon;
import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.objects.ObjectArrayList;
import it.unimi.dsi.fastutil.objects.ObjectList;
import mgi.utilities.StringFormatUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

import static com.zenyte.game.content.skills.slayer.SlayerMaster.*;

/**
 * <AUTHOR> | 26. okt 2017 : 11:27.54
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>} TODO: Check over the quantities
 * as mid-masters are all
 * the same and not equal to RS. TODO: Read over all of the names and make sure there are no exceptions for
 * singularities. TODO: Add
 * more possible monsters that may be slain for all given tasks, a lot are missing, such as superiors.
 */
public enum RegularTask implements SlayerTask {
    BANSHEES(new Task[]{new Task(TURAEL, 8, 15, 30), new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 6, 60, 120),
            new Task(CHAELDAR, 5, 110, 170)}, 38, 15, 20, "Banshees use a piercing scream to shock their enemies, you'll need some Earmuffs to protect yourself from them.",
            List.of(
                new SlayerLocationInfo("Slayer Tower - First Floor", new Location(3439, 3546, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1616, 9997, 0))
            ),
            "Banshee", "Twisted Banshee", "Screaming banshee", "Screaming twisted banshee"),
    BATS(new Task[]{new Task(TURAEL, 7, 15, 30), new Task(MAZCHNA, 7, 40, 70)}, 8, 1, 5, "Bats are rarely found on the ground, so you'll have to fight them while they're airborne, which won't be easy for melee.",
            List.of(
                new SlayerLocationInfo("Taverley Dungeon (Bats)", new Location(2909, 9831, 0)),
                new SlayerLocationInfo("Outside Slayer Tower (Bats)", new Location(3429, 3522, 0)),
                new SlayerLocationInfo("Coal Trucks Area (Giant Bats)", new Location(2579, 3477, 0))
            ),
            "Bat", "Giant bat", "Albino bat", "Deathwing") {
    },
    BIRDS(new Task[]{new Task(TURAEL, 6, 15, 30)}, 5, 1, 0,
            "Birds aren't the most intelligent of creatures, but watch out for their sharp stabbing beaks.",
            List.of(
                new SlayerLocationInfo("Lumbridge Farms (Chickens)", new Location(3177, 3295, 0)),
                new SlayerLocationInfo("Port Sarim Pier (Seagulls)", new Location(3029, 3236, 0)),
                new SlayerLocationInfo("Gnome Stronghold (Terrorbirds)", new Location(2379, 3432, 0))
            ),
            "Duck", "Chicken", "Undead Chicken", "Rooster", "Seagull", "Penguin", "Entrana " +
            "firebird", "Bird", "Chompy bird", "Terrorbird", "Mounted Terrorbird", "Mounted terrorbird gnome",
            "Vulture", "Oomlie bird", "Baby roc") {
    },
    BEARS(new Task[]{new Task(TURAEL, 7, 15, 30), new Task(MAZCHNA, 6, 40, 70), new Task(KRYSTILIA, 6, 50, 100)}, 13,
            1, 13, "Bears are tough creatures and fierce fighters, watch out for their powerful claws.",
            List.of(
                new SlayerLocationInfo("Goblin Village (Black bears)", new Location(2973, 3487, 0)),
                new SlayerLocationInfo("NE of Ardougne (Grizzly bears)", new Location(2701, 3332, 0)),
                new SlayerLocationInfo("NW of Ferox Enclave (Grizzly Bears)", new Location(3134, 3638, 0))
            ),
            "Black bear", "Bear cub", "Grizzly bear", "Grizzly bear cub", "Callisto", "Bear", "Reanimated bear", "Artio"),
    CAVE_BUGS(new Task[]{new Task(TURAEL, 8, 10, 20), new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 7, 60, 120)},
            63, 7, 0, "Cave Bugs are like Cave Crawlers, except smaller and easier to squish, though they still have a fondness for plants.",
            List.of(
                new SlayerLocationInfo("Lumbridge Swamp Caves (<col=ff0000>Light Source</col>)", new Location(3184, 9557, 0)),
                new SlayerLocationInfo("Dorgesh-Kaan South Dungeon", new Location(2715, 5236, 0))
            ),
            "Cave bug"),
    CAVE_CRAWLERS(new Task[]{new Task(TURAEL, 8, 15, 20), new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 7, 60, 120)
            , new Task(CHAELDAR, 5, 110, 170)}, 37, 10, 10, "Cave Crawlers are small and fast, often hiding in ambush. Avoid their barbed tongue or you'll get poisoned.",
            List.of(
                new SlayerLocationInfo("Fremennik Slayer Dungeon", new Location(2790, 9996, 0)),
                new SlayerLocationInfo("Lumbridge Swamp Caves (<col=ff0000>Light Source</col>)", new Location(3191, 9573, 0)),
                new SlayerLocationInfo("Dorgesh-Kaan South Dungeon", new Location(2712, 5208, 0))
            ),
            "Cave crawler", "Chasm crawler"),
    CAVE_SLIMES(new Task[]{new Task(TURAEL, 8, 10, 20), new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 7, 60, 120),
            new Task(CHAELDAR, 6, 110, 170)}, 62, 17, 15, "Cave Slimes are the lesser cousins of Jellies, though don't be fooled they can still be dangerous as they're often poisonous.",
            List.of(
                new SlayerLocationInfo("Lumbridge Swamp Caves (<col=ff0000>Light Source</col>)", new Location(3153,9557, 0)),
                new SlayerLocationInfo("Dorgesh-Kaan South Dungeon", new Location(2703, 5227, 0))
            ),
            "Cave slime"),
    COWS(new Task[]{new Task(TURAEL, 8, 15, 30)}, 6, 1, 5, "Cows are bigger than you, so they'll often hit fairly hard but are usually fairly slow to react.",
            List.of(
                new SlayerLocationInfo("Crafting Guild", new Location(2924, 3285, 0)),
                new SlayerLocationInfo("Lumbridge North", new Location(3180, 3328, 0)),
                new SlayerLocationInfo("Lumbridge East", new Location(3259, 3273, 0))
            ),
            "Cow", "Cow calf", "Undead cow", "Unicow"),
    CRAWLING_HANDS(new Task[]{new Task(TURAEL, 8, 15, 30), new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 6, 60,
            120)}, 39, 5, 0, "Crawling Hands are undead severed hands, fast and dexterous they claw their victims.",
            List.of(
                new SlayerLocationInfo("Slayer Tower - 1st Floor", new Location(3427, 3549, 0))
            ),
            "Crawling hand", "Crushing hand"),
    DESERT_LIZARDS(new Task[]{new Task(TURAEL, 8, 15, 30), new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 7, 60,
            120), new Task(CHAELDAR, 5, 110, 170)}, 68, 22, 15, "Desert Lizards are large reptiles with tough skin. They're cold-blooded, so dousing them with freezing water will finish them off after a tough battle.",
            List.of(
                new SlayerLocationInfo("Kharidian Desert (Desert Lizards) - <col=ff0000>Waterskins</col>", new Location(3430, 3059, 0)),
                new SlayerLocationInfo("Karuulm Slayer Dungeon (Sulphur Lizards) - <col=ff0000>Boots of Stone</col>", new Location(3430, 3059, 0))
            ),
            "Desert lizard", "Lizard", "Small lizard", "Sulphur Lizard"),
    DOGS(new Task[]{new Task(TURAEL, 7, 15, 30), new Task(MAZCHNA, 7, 40, 70)}, 22, 1, 15, "Dogs are much like Wolves, they are pack creatures which will hunt in groups.",
            List.of(
                new SlayerLocationInfo("Kharidian Desert (Jackals) - <col=ff0000>Waterskins</col>", new Location(3335, 2895, 0)),
                new SlayerLocationInfo("McGrubor's Woods (Guard Dogs)", new Location(2653, 3477, 0)),
                new SlayerLocationInfo("Brimhaven Dungeon (Wild Dogs)", new Location(2666, 9523, 0))
            ),
            "Wild dog", "Guard dog", "Jackal", "Dog", "Reanimated dog"),
    DWARVES(new Task[]{new Task(TURAEL, 7, 15, 30)}, 57, 1, 6, "Dwarves are a small but tough race of miners, often using pickaxes to pierce their opponents armour.",
            List.of(
                new SlayerLocationInfo("Taverley Dungeon (Chaos Dwarves)", new Location(2924, 9761, 0)),
                new SlayerLocationInfo("Ice Mountain", new Location(3016, 3450, 0)),
                new SlayerLocationInfo("Falador Mine", new Location(3022, 9822, 0))
            ),
            "Dwarf", "Dwarf gang member", "Chaos dwarf", "Black Guard"),
    GHOSTS(new Task[]{new Task(TURAEL, 7, 15, 30), new Task(MAZCHNA, 7, 40, 70)}, 12, 1, 13, "Ghosts are undead so magic is your best bet against them, there is even a spell specially for fighting the undead.",
            List.of(
                new SlayerLocationInfo("Taverley Dungeon", new Location(2909, 9849, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1693, 10063, 0)),
                new SlayerLocationInfo("Stronghold of Security", new Location(2353, 5196, 0))
            ),
            "Ghost", "Tortured soul"),
    GOBLINS(new Task[]{new Task(TURAEL, 7, 15, 30)}, 2, 1, 0, "Goblins are mostly just annoying, but they can be vicious. Watch out for the spears they sometimes carry.",
            List.of(
                new SlayerLocationInfo("Goblin Village", new Location(2956, 3504, 0)),
                new SlayerLocationInfo("Lumbridge", new Location(3256, 3246, 0)),
                new SlayerLocationInfo("Goblin Cave", new Location(2585, 9834, 0))
            ),
            "Goblin", "Reanimated goblin", "Cave goblin guard", "Goblin Champion", "Sergeant strongstack", "Sergeant grimspike"
            , "Sergeant steelwill"),
    ICEFIENDS(new Task[]{new Task(TURAEL, 8, 15, 30)}, 75, 1, 20, "Icefiends are beings of ice and freezing rock, they're quick and agile so you'll want to be careful when getting close to them.",
            List.of(
                new SlayerLocationInfo("Ice Mountain", new Location(3010, 3489, 0))
            ),
            "Icefiend"),
    KALPHITE(new Task[]{new Task(TURAEL, 6, 15, 30), new Task(MAZCHNA, 6, 40, 70), new Task(VANNAKA, 7, 60, 120),
            new Task(CHAELDAR, 11, 110, 170), new Task(NIEVE, 9, 120, 185), new Task(DURADEL, 9, 130, 200),
            new Task(KONAR_QUO_MATEN, 9, 120, 170, KalphiteLair.class, KalphiteCave.class)}, 53, 1, 15, "Kalphite are large insects which live in great hives under the desert sands.",
            List.of(
                new SlayerLocationInfo("Kalphite Lair", new Location(3500, 9521, 2)),
                new SlayerLocationInfo("Kalphite Cave", new Location(3299, 9500, 0))
            ),
            "Kalphite worker", "Kalphite soldier", "Kalphite guardian", "Kalphite queen", "Reanimated kalphite"),
    MINOTAURS(new Task[]{new Task(TURAEL, 7, 10, 20)}, 76, 1, 7, "Minotaurs are large manlike creatures but you'll want to be careful of their horns.",
            List.of(
                new SlayerLocationInfo("Stronghold of Security", new Location(1875, 5215, 0))
            ),
            "Minotaur", "Reanimated minotaur"),
    MONKEYS(new Task[]{new Task(TURAEL, 6, 15, 30)}, 1, 1, 0, "Monkeys are tricky creatures, they are agile and fairly fast. Learn to anticipate their movements.",
            List.of(
                new SlayerLocationInfo("Karamja Jungle (Monkeys)", new Location(2864, 3023, 0)),
                new SlayerLocationInfo("Kruk's Dungeon (Maniacal Monkeys) - (<col=ff0000>Light Source</col>)", new Location(2441, 9165, 1)),
                new SlayerLocationInfo("Ape Atoll (Monkey Guards)", new Location(2800, 2797, 1)),
                new SlayerLocationInfo("Crash Site Cavern (Demonic Gorillas)", new Location(2126, 5645, 0))
            ),
            "Monkey", "Reanimated monkey", "Monkey guard", "Monkey archer", "Zombie monkey",
            "Aberab", "Awowogei", "Bonzara", "Daga", "Dugopul", "Duke", "Elder guard", "Hamab", "Ifaba", "Iwazaru",
            "Jumaane", "Kikazaru", "Kruk", "Mizaru", "Monkey Child", "Muruwoi", "Oobapohk", "Padulah", "Solihib",
            "Sleeping monkey", "Trefaji", "Tutab", "Uwogo", "Tortured gorilla", "Maniacal monkey", "Maniacal monkey archer", "Demonic gorilla"),
    RATS(new Task[]{new Task(TURAEL, 7, 15, 30)}, 3, 1, 0, "Rats are your everyday pest, but they can get fairly big. Watch out for their sharp piercing teeth.",
            List.of(
                new SlayerLocationInfo("Lumbridge Swamp", new Location(3198, 3175, 0)),
                new SlayerLocationInfo("Ardougne Sewers", new Location(2645, 9708, 0)),
                new SlayerLocationInfo("West Ardougne", new Location(2483, 3313, 0)),
                new SlayerLocationInfo("Brine Rat Cavern", new Location(2706, 10132, 0))
            ),
            "Rat", "Giant rat", "Dungeon rat", "Crypt rat", "Brine rat", "Giant crypt rat", "Blessed giant rat", "Angry giant rat"),
    SCORPIONS(new Task[]{new Task(TURAEL, 7, 15, 30), new Task(MAZCHNA, 7, 40, 70), new Task(KRYSTILIA, 6, 60, 100)},
            7, 1, 7, "Scorpions are almost always poisonous, their hard carapace makes them resistant to crushing and stabbing attacks.",
            List.of(
                new SlayerLocationInfo("Al Kharid Mine", new Location(3299, 3301, 0)),
                new SlayerLocationInfo("Falador Mine", new Location(3046, 9785, 0))
            ),
            "Scorpion", "Reanimated scorpion", "King scorpion", "Poison scorpion", "Pit scorpion", "Scorpia", "Scorpia's offspring"),
    SKELETONS(new Task[]{new Task(TURAEL, 7, 15, 30), new Task(MAZCHNA, 6, 40, 70), new Task(KRYSTILIA, 5, 60, 100)},
            11, 1, 15, "Skeletons are undead so magic is your best bet against them, there is even a spell specially for fighting the undead.",
            List.of(
                new SlayerLocationInfo("Taverley Dungeon", new Location(2901, 9849, 0)),
                new SlayerLocationInfo("Edgeville Dungeon", new Location(3132, 9909, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1641, 10047, 0)),
                new SlayerLocationInfo("Ape Atoll", new Location(2724, 9138, 0))
            ),
            "Skeleton", "Skeleton mage", "Skeleton champion", "Calvar'ion", "Calvar'ion reborn", "Vet'ion", "Vet'ion reborn"),
    SPIDERS(new Task[]{new Task(TURAEL, 6, 15, 30), new Task(KRYSTILIA, 6, 60, 100)}, 4, 1, 0, "Spiders are often poisonous, and many varieties are camouflaged too.",
            List.of(
                new SlayerLocationInfo("Lumbridge - Outside HAM Hideout", new Location(3168, 3246, 0)),
                new SlayerLocationInfo("Stronghold of Security", new Location(2123, 5273, 0)),
                new SlayerLocationInfo("Varrock Sewers (Deadly Red Spiders)", new Location(3179, 9884, 0))
            ),
            "Spider", "Giant spider", "Shadow spider", "Giant crypt spider", "Venenatis", "Kalrag", "Jungle spider", "Deadly " +
            "red spider", "Blessed spider", "Crypt spider", "Poison spider", "Temple spider", "Sarachnis", "Spindel", "araxxor"),
    ARAXYTES(new Task[]{
            new Task(TURAEL, 6, 15, 30),
            new Task(NIEVE, 6, 40, 60),
            new Task(DURADEL, 6, 60, 80)
    }, 4, 92, 0,
            "High level spiders that can inflict venom damage. //TODO - Slayer TIP",
            List.of(
                new SlayerLocationInfo("Araxyte Hive", new Location(3677, 9823, 0)),
                new SlayerLocationInfo("Araxxor's Lair", new Location(3658, 9816, 0))
            ),
            "araxxor", "araxyte", "dreadborn araxyte"),
    WOLVES(new Task[]{new Task(TURAEL, 7, 15, 30), new Task(MAZCHNA, 7, 40, 70)}, 9, 1, 20, "Wolves are pack animals, so you'll always find them in groups. Watch out for their bite, it can be nasty.",
            List.of(
                new SlayerLocationInfo("Stronghold of Security", new Location(1894, 5191, 0)),
                new SlayerLocationInfo("White Wolf Mountain", new Location(2832, 3497, 0)),
                new SlayerLocationInfo("Rellekka Area", new Location(2640, 3635, 0)),
                new SlayerLocationInfo("NW of Prifddinas (Dire Wolf)", new Location(3172, 6161, 0))
            ),
            "Wolf", "White wolf", "Big wolf", "Dire wolf", "Jungle wolf", "Desert wolf", "Ice wolf"),

    ZOMBIES(new Task[]{new Task(TURAEL, 7, 15, 30), new Task(MAZCHNA, 7, 40, 70)}, 10, 1, 10, "Zombies are undead so magic is your best bet against them, there is even a spell specially for fighting the undead.",
            List.of(
                new SlayerLocationInfo("Draynor Sewers", new Location(3091, 9672, 0)),
                new SlayerLocationInfo("Varrock Sewers", new Location(3226, 9906, 0)),
                new SlayerLocationInfo("Stronghold of Security", new Location(2000, 5190, 0)),
                new SlayerLocationInfo("Alice's Farm (Undead Cows & Chickens)", new Location(3264, 3528, 0)),
                new SlayerLocationInfo("Ungael Island (Vorkath)", new Location(2272, 4049, 0))
            ),
            "Zombie", "Summoned zombie", "Zombie pirate", "Zombie swab", "Zombies champion", "Zombie rat", "Monkey zombie", "Small zombie monkey", "Large zombie monkey", "Undead cow", "Undead chicken", "Vorkath"),
    CATABLEPON(new Task[]{new Task(MAZCHNA, 8, 40, 70)}, 78, 1, 35, "Catablepon are mythical, cow like, magical creatures. Beware their weakening glare.",
            List.of(
                new SlayerLocationInfo("Stronghold of Security", new Location(2119, 5288, 0))
            ),
            "Catablepon"),
    COCKATRICE(new Task[]{new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 8, 60, 120), new Task(CHAELDAR, 6, 110,
            170)}, 44, 25, 25, "Cockatrice, like Basilisks, have a gaze which will paralyse and harm their prey. You'll need a Mirror Shield to protect you.", player -> player.getSkills().getLevelForXp(SkillConstants.DEFENCE) >= 20, null,
            List.of(
                new SlayerLocationInfo("Fremennik Slayer Dungeon", new Location(2793, 10041, 0))
            ),
            "Cockatrice", "Cockathrice"),
    EARTH_WARRIORS(new Task[]{new Task(MAZCHNA, 6, 40, 70), new Task(VANNAKA, 6, 60, 120), new Task(KRYSTILIA, 6, 75,
            130)}, 54, 1, 35, "Earth Warriors are a kind of earth elemental, grind them to dust with blunt weapons.",
            List.of(
                new SlayerLocationInfo("Edgeville Dungeon", new Location(3132, 9916, 0))
            ),
            "Earth warrior"),
    FLESH_CRAWLERS(new Task[]{new Task(MAZCHNA, 7, 40, 70)}, 77, 1, 15, "Fleshcrawlers are scavengers and will eat you - and anyone else, given the chance.",
            List.of(
                new SlayerLocationInfo("Stronghold of Security", new Location(2045, 5196, 0))
            ),
            "Flesh crawler"),
    GHOULS(new Task[]{new Task(MAZCHNA, 7, 40, 70), new Task(VANNAKA, 7, 60, 120)}, 23, 1, 25, "Ghouls aren't undead but they are stronger and tougher than they look. However they're also very cowardly and will run if they're losing a fight.",
            List.of(
                new SlayerLocationInfo("Salve Graveyard (Canifis)", new Location(3439, 3466, 0)),
                new SlayerLocationInfo("Outside Slayer Tower", new Location(3421, 3507, 0))
            ),
            "Ghoul", "Ghoul champion"),
    HILL_GIANTS(new Task[]{new Task(MAZCHNA, 7, 40, 70), new Task(VANNAKA, 7, 60, 120)}, 14, 1, 25, "Hill Giants often wield large weapons, learn to recognise what kind of weapon it is and act accordingly.",
            List.of(
                new SlayerLocationInfo("Edgeville Dungeon (<col=ff0000>Brass Key</col>)", new Location(3115, 3448, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1660, 10072, 0)),
                new SlayerLocationInfo("Giant Pit", new Location(1451, 3613, 0)),
                new SlayerLocationInfo("Warrior's Guild (Cyclops)", new Location(2843, 3541, 0))
            ),
            "Hill giant", "Giant champion", "Cyclops"),
    HOBGOBLIN(new Task[]{new Task(MAZCHNA, 7, 40, 70), new Task(VANNAKA, 7, 60, 120)}, 21, 1, 20, "Hobgoblins are sneaky underhanded creatures, they often wield spears and some times carry javelines too.",
            List.of(
                new SlayerLocationInfo("Edgeville Dungeon", new Location(3128, 9878, 0)),
                new SlayerLocationInfo("Hobgoblin Peninsula", new Location(2929, 3264, 0))
            ),
            "Hobgoblin", "Hobgoblin champion"),
    ICE_WARRIORS(new Task[]{new Task(MAZCHNA, 7, 40, 70), new Task(VANNAKA, 7, 60, 120), new Task(KRYSTILIA, 7, 100,
            150)}, 19, 1, 45, "Ice Warriors are a kind of ice elemental, shatter them with blunt weapons or melt them with fire.",
            List.of(
                new SlayerLocationInfo("Asgarnian Ice Dungeon", new Location(3043, 9590, 0)),
                new SlayerLocationInfo("Ice Queen Lair", new Location(2869, 9940, 0))
            ),
            "Ice warrior", "Ice Queen"),
    /*KILLERWATTS(new Task[] { new Task(MAZCHNA, 6, 40, 70), new Task(VANNAKA, 6, 60,
            120) }, 73, 37, 50, "Killerwatts are electrical creatures. You may want to insulte yourself from the " +
            "ground.", "Killerwatt"),*/
    MOGRES(new Task[]{new Task(MAZCHNA, 6, 40, 70), new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 6, 110, 170)},
            67, 32, 30, "Mogres are a type of aquatic Ogre that is often mistaken for a giant mudskipper. You have to force them out of the water with a fishing explosive.",
            List.of(
                new SlayerLocationInfo("Mudskipper Point", new Location(2996, 3120, 0))
            ),
            "Mogre"),
    PYREFIEND(new Task[]{new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 8, 60, 120),
            new Task(CHAELDAR, 6, 110, 170)}, 47, 30, 25, "Pyrefiends are beings of fire and molten rock, they're quick and agile so you'll want to be careful when getting close to them.",
            List.of(
                new SlayerLocationInfo("Fremennik Slayer Dungeon", new Location(2765, 10017, 0))
            ),
            "Pyrefiend", "Flaming pyrelord"),
    ROCKSLUGS(new Task[]{new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 7, 60, 120),
            new Task(CHAELDAR, 5, 110, 170)}, 51, 20, 20, "Rockslugs are strange stoney slugs. You'll need to fight them to near death before finishing them off with Salt.",
            List.of(
                new SlayerLocationInfo("Fremennik Slayer Dungeon", new Location(2787, 10018, 0))
            ),
            "Rockslug", "Giant rockslug"),
    SHADES(new Task[]{new Task(MAZCHNA, 8, 40, 70), new Task(VANNAKA, 8, 60, 120)}, 64, 1, 30, "Shades are undead so magic is your best best against them, you can find Shades at Mort'ton.",
            List.of(
                new SlayerLocationInfo("Mort'ton", new Location(3488, 3290, 0)),
                new SlayerLocationInfo("Shade Catacombs", new Location(3493, 9725, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(3493, 9725, 0))
            ),
            "Shade", "Loar shade", "Phrin shade", "Riyl shade", "Asyn shade", "Fiyr shade"),
    /*VAMPIRES(new Task[] { new Task(MAZCHNA, 6, 40, 70), new Task(VANNAKA, 7, 60,
            120) }, 34, 1, 35, "Vampires are dangerous humanoid creatures, biting anything they can. Bringing garlic " +
            "may be of great help.", "Vampire"),*/
    WALL_BEASTS(new Task[]{new Task(MAZCHNA, 7, 10, 20), new Task(VANNAKA, 6, 60, 120), new Task(CHAELDAR, 6, 110,
            170)}, 61, 35, 30,
            "Wall Beasts are really much larger creatures but you'll only see their arms. You'll want something sharp on your head to stop them grabbing you.",
            player -> player.getSkills().getLevelForXp(SkillConstants.DEFENCE) >= 5, null,
            List.of(
                new SlayerLocationInfo("Lumbridge Swamp Caves (<col=ff0000>Light Source</col>)", new Location(3169, 9573, 0))
            ),
            "Wall beast"),
    ABYSSAL_DEMONS(new Task[]{new Task(VANNAKA, 5, 60, 120), new Task(KRYSTILIA, 5, 75, 125), new Task(CHAELDAR, 12, 110, 170), new Task(NIEVE, 9, 120
            , 185), new Task(DURADEL, 12, 130, 250), new Task(KONAR_QUO_MATEN, 9, 120, 170, CatacombsOfKourend.class,
            SlayerTower.class)}, 42, 85, 85, "Abyssal Demons are nasty creatures to fight. They aren't really part of this realm, and are able to move very quickly to trap their prey.",
            List.of(
                new SlayerLocationInfo("Slayer Tower - Basement & Top Floor", new Location(3440, 9962, 3)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1674, 10091, 0))
            ),
            Range.of("Augment my abbies", 200, 250), "Abyssal demon", "Abyssal sire", "Greater abyssal demon", "Reanimated abyssal"),
    ABERRANT_SPECTRES(new Task[]{new Task(VANNAKA, 8, 60, 120), new Task(CHAELDAR, 8, 110, 170), new Task(NIEVE, 6,
            120, 185), new Task(DURADEL, 7, 130, 200), new Task(KONAR_QUO_MATEN, 6, 120, 170,
            CatacombsOfKourend.class, SlayerTower.class, StrongholdSlayerDungeon.class)}, 41, 60, 65, "Aberrant Spectres are fetid, vile ghosts. The very smell of them will paralyse and harm you. A nosepeg will help ignore their stink.",
            List.of(
                new SlayerLocationInfo("Slayer Tower - 2nd Floor", new Location(3426, 3556, 1)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1470, 3652, 0)),
                new SlayerLocationInfo("Stronghold Slayer Cave", new Location(2438, 9784, 0))
            ),
            Range.of("Smell ya later", 200, 250), "Aberrant spectre", "Deviant spectre", "Abhorrent spectre",
            "Repugnant spectre"),
    ANKOU(new Task[]{new Task(VANNAKA, 7, 30, 60), new Task(NIEVE, 5, 50, 90), new Task(DURADEL, 5, 40, 130),
            new Task(KRYSTILIA, 6, 40, 130), new Task(KONAR_QUO_MATEN, 5, 50, 50, StrongholdOfSecurity.class,
            StrongholdSlayerDungeon.class, CatacombsOfKourend.class)}, 79, 1, 40, "Ankou are EVERYWHERE!!!!",
            List.of(
                new SlayerLocationInfo("Stronghold of Security", new Location(2354, 5245, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1631, 9992, 0)),
                new SlayerLocationInfo("Stronghold Slayer Cave", new Location(2471, 9813, 0))
            ),
            Range.of("Ankou very much", 91, 149), "Ankou"),
    BASILISKS(new Task[]{new Task(VANNAKA, 8, 60, 120), new Task(CHAELDAR, 7, 110, 170), new Task(KONAR_QUO_MATEN, 5, 110, 170, FremennikSlayerDungeon.class, JormungandPrisonDungeon.class), new Task(NIEVE, 6, 120, 185), new Task(DURADEL, 7, 130, 200)},
            43, 40, 40, "Basilisks, like Cockatrice, have a gaze which will paralyse and harm their prey. You'll need a Mirror Shield to protect you.", player -> player.getSkills().getLevelForXp(SkillConstants.DEFENCE) >= 20, null,
            List.of(
                new SlayerLocationInfo("Fremennik Slayer Dungeon", new Location(2748, 9998, 0)),
                new SlayerLocationInfo("Jormungand's Prison", new Location(2461, 10417, 0))
            ),
            "Basilisk", "Monstrous basilisk", "Basilisk Knight"),
    BLUE_DRAGONS(new Task[]{new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 8, 110, 170), new Task(NIEVE, 4, 120,
            185), new Task(DURADEL, 4, 138, 150), new Task(KONAR_QUO_MATEN, 4, 120, 170, CatacombsOfKourend.class,
            TaverleyDungeon.class, MythsGuildBasement.class)}, 25, 1, 65, "Blue Dragons aren't as strong as other dragons but they're still very powerful, watch out for their fiery breath.",
            List.of(
                new SlayerLocationInfo("Taverley Dungeon", new Location(2923, 9804, 0)),
                new SlayerLocationInfo("Catacombs of Kourend (Brutal Blue Dragons)", new Location(1642, 10073, 0)),
                new SlayerLocationInfo("Myths' Guild Basement", new Location(1926, 8965, 1)),
                new SlayerLocationInfo("Ungael Island (Vorkath)", new Location(2272, 4049, 0))
            ),
            "Blue dragon", "Baby blue dragon", "Vorkath", "Brutal blue dragon", "Baby blue dragon", 241, 242, 243),
    BLOODVELD(new Task[]{new Task(VANNAKA, 8, 60, 120), new Task(CHAELDAR, 8, 110, 170), new Task(NIEVE, 9, 120, 185)
            , new Task(DURADEL, 8, 130, 200), new Task(KONAR_QUO_MATEN, 9, 120, 170, CatacombsOfKourend.class,
            MeiyerditchLaboratoriesArea.class, SlayerTower.class, StrongholdSlayerDungeon.class, IorwerthDungeon.class)}, 48, 50, 50, "Bloodveld are strange demonic creatures, they use their long rasping tongue to feed on just about anything they can find.",
            List.of(
                new SlayerLocationInfo("Slayer Tower - Basement & 2nd Floor", new Location(3415, 9937, 3)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1679, 10075, 0)),
                new SlayerLocationInfo("Stronghold Slayer Cave", new Location(2456, 9826, 0)),
                new SlayerLocationInfo("Meiyerditch Laboratories", new Location(3605, 9738, 0)),
                new SlayerLocationInfo("Iowerth Dungeon", new Location(3246, 12430, 0))
            ),
            Range.of("Bleed me dry", 200, 250), "Bloodveld", "Mutated bloodveld", "Insatiable bloodveld", "Insatiable mutated bloodveld", "Reanimated bloodveld"),
    //BRINE_RATS(new Task[] { new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 7, 110, 170),
    //		new Task(NIEVE, 3, 160, 200) }, 84, 47, 45, "Brine rats are large, bald vermin found in dark dungeons.",
    //        "Brine rat"),
    BRONZE_DRAGONS(new Task[]{new Task(VANNAKA, 7, 30, 60), new Task(CHAELDAR, 8, 60, 120), new Task(KONAR_QUO_MATEN,
            5, 30, 50, CatacombsOfKourend.class, BrimhavenDungeon.class)}, 58, 1, 75, "Bronze Dragons are the weakest of the metallic dragons, their bronze scales are far thicker than normal bronze armour.",
            List.of(
                new SlayerLocationInfo("Brimhaven Dungeon", new Location(2722, 9484, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1662, 10076, 0))
            ),
            Range.of("Pedal to the metals", 30, 50), "Bronze dragon"),
    CROCODILES(new Task[]{new Task(VANNAKA, 6, 60, 120)}, 65, 1, 50, "Crocodiles are large reptiles which live near water. You'll want to have a stabbing weapon handy for puncturing their thick scaly hides.",
            List.of(
                new SlayerLocationInfo("Kharidian Desert", new Location(3292, 2921, 0))
            ),
            "Crocodile"),
    DAGANNOTH(new Task[]{new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 11, 110, 170), new Task(NIEVE, 8, 120,
            185), new Task(DURADEL, 9, 130, 200), new Task(KONAR_QUO_MATEN, 8, 120, 170, CatacombsOfKourend.class,
            LighthouseDungeon.class, WaterbirthDungeon.class)}, 35, 1, 75, "Dagannoth are large sea dwelling creatures which are very aggressive. You'll often find them in caves near sea water.",
            List.of(
                new SlayerLocationInfo("Waterbirth Island Dungeon", new Location(2523, 3740, 0)),
                new SlayerLocationInfo("Lighthouse Dungeon", new Location(2515, 4628, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1665, 9987, 0)),
                new SlayerLocationInfo("Dagannoth Kings", new Location(1918, 4363, 0))
            ),
            "Dagannoth fledgeling", "Dagannoth spawn", "Dagannoth rex", "Dagannoth prime", "Dagannoth supreme", "Dagannoth mother", "Dagannoth", "Reanimated dagannoth"),
    DUST_DEVILS(new Task[]{new Task(VANNAKA, 8, 60, 120), new Task(KRYSTILIA, 5, 75, 125), new Task(CHAELDAR, 9, 110, 170), new Task(NIEVE, 6, 120,
            185), new Task(DURADEL, 5, 130, 200), new Task(KONAR_QUO_MATEN, 6, 120, 170, CatacombsOfKourend.class,
            SmokeDungeonArea.class)}, 49, 65, 70, "Dust Devils use clouds of dust, sand, ash, and whatever else they can inhale to blind and disorientate their victims.",
            List.of(
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1715, 10024, 0)),
                new SlayerLocationInfo("Smoke Dungeon", new Location(3206, 9379, 0))
            ),
            Range.of("To dust you shall return", 200, 250), "Dust devil", "Choke devil"),
    ELVES(new Task[] { new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 8, 110, 170), new Task(NIEVE, 4, 60, 90),
    new Task(DURADEL, 4, 100,
            170) }, 56, 1, 70, "Elves are quick, agile, and vicious fighters which often favour bows and polearms.",
            List.of(
                new SlayerLocationInfo("Iowerth Dungeon", new Location(3201, 12418, 0)),
                new SlayerLocationInfo("Iowerth Camp", new Location(2196, 3251, 0)),
                new SlayerLocationInfo("Lletya", new Location(2332, 3171, 0))
            ),
            "Elf warrior", "Iowerth Warrior", "Iowerth Archer", ""),
    FEVER_SPIDERS(new Task[]{new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 7, 110, 170)}, 69, 42, 40, "Fever Spiders are giant spiders that carry the deadly Spider Fever. If you don't want to catch it I suggest you wear Slayer Gloves to fight them.",
            List.of(
                new SlayerLocationInfo("Braindeath Island", new Location(2150, 5099, 0))
            ),
            "Fever spider"),
    FIRE_GIANTS(new Task[]{new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 12, 110, 170), new Task(NIEVE, 9, 120,
            190), new Task(DURADEL, 7, 130, 200), new Task(KRYSTILIA, 7, 100, 150), new Task(KONAR_QUO_MATEN, 9, 120,
            170, KaruulmSlayerDungeon.class, BrimhavenDungeon.class, WaterfallDungeon.class,
            StrongholdSlayerDungeon.class, CatacombsOfKourend.class)}, 16, 1, 65, "Like other giants, Fire Giants often wield large weapons, learn to recognise what kind of weapon it is and act accordingly.",
            List.of(
                new SlayerLocationInfo("Brimhaven Dungeon", new Location(2649, 9508, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1623, 10039, 0)),
                new SlayerLocationInfo("Karuulm Slayer Dungeon (<col=ff0000>Boots of Stone</col>)", new Location(1296, 10207, 2)),
                new SlayerLocationInfo("Stronghold Slayer Cave", new Location(2407, 9778, 0)),
                new SlayerLocationInfo("Waterfall Dungeon", new Location(2576, 9883, 0))
            ),
            "Fire giant"),
    GARGOYLES(new Task[]{new Task(VANNAKA, 5, 60, 120), new Task(CHAELDAR, 11, 110, 170), new Task(NIEVE, 6, 120,
            185), new Task(DURADEL, 8, 130, 244), new Task(KONAR_QUO_MATEN, 5, 120, 170, SlayerTower.class)}, 46, 75,
            80, "Gargoyles are winged creatures of stone. You'll need to fight them to near death before breaking them apart with a Rock Hammer.",
            List.of(
                new SlayerLocationInfo("Slayer Tower - Basement & Top Floor", new Location(3424, 9944, 3))
            ),
            Range.of("Get smashed", 200, 250), "Gargoyle", "Dusk", "Marble gargoyle") {
        @Override
        public float getExperience(final NPC npc) {
            if (npc instanceof Dusk) {
                return 1350;
            }
            return npc.getMaxHitpoints() * (npc instanceof SuperiorNPC ? 10 : 1);
        }
    },
    GREEN_DRAGONS(new Task[]{new Task(VANNAKA, 6, 30, 60), new Task(KRYSTILIA, 4, 60, 100)}, 24, 1, 52, "Green Dragons are the weakest dragon but still very powerful, watch out for their fiery breath.",
            List.of(
                new SlayerLocationInfo("Myths' Guild Basement", new Location(1949, 8989, 1)),
                new SlayerLocationInfo("<col=0000FF>West Wilderness Green Dragons</col>", new Location(2977, 3596, 0)),
                new SlayerLocationInfo("<col=0000FF>East Wilderness Green Dragons</col>", new Location(3347, 3667, 0))
            ),
            "Green dragon", "Baby green dragon", "Brutal green dragon", 5194, 5872, 5873),
    HARPIE_BUG_SWARMS(new Task[]{new Task(VANNAKA, 8, 30, 60), new Task(CHAELDAR, 6, 60, 120)}, 70, 33, 45, "Harpie Bug Swarms are pesky critters that are hard to hit. You need a lit bug lantern to distract them with its hypnotic light.",
            player -> player.getSkills().getLevelForXp(SkillConstants.FIREMAKING) >= 33, null,
            List.of(
                new SlayerLocationInfo("Karamja", new Location(2874, 3111, 0))
            ),
            "Harpie bug swarm"),
    HELLHOUNDS(new Task[]{new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 9, 110, 170), new Task(NIEVE, 8, 120,
            185), new Task(DURADEL, 10, 130, 200), new Task(KRYSTILIA, 7, 70, 123), new Task(KONAR_QUO_MATEN, 8, 120,
            170, CatacombsOfKourend.class, StrongholdSlayerDungeon.class, TaverleyDungeon.class,
            KaruulmSlayerDungeon.class)}, 31, 1, 75, "Hellhounds are a cross between Dogs and Demons, they are dangerous with a fierce bite.",
            List.of(
                new SlayerLocationInfo("Taverley Dungeon", new Location(2856, 9832, 0)),
                new SlayerLocationInfo("Stronghold Slayer Cave", new Location(2421, 9804, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1640, 10054, 0)),
                new SlayerLocationInfo("Karuulm Slayer Dungeon (<col=ff0000>Boots of Stone</col>)", new Location(1329, 10208, 2)),
                new SlayerLocationInfo("Cerberus Lair", new Location(1310, 1250, 0))
            ),
            "Hellhound", "Cerberus"),
    ICE_GIANTS(new Task[]{new Task(VANNAKA, 7, 60, 120), new Task(KRYSTILIA, 6, 100, 160)}, 15, 1, 50, "Like other giants, Ice Giants often wield large weapons, learn to recognise what kind of weapon it is and act accordingly.",
            List.of(
                new SlayerLocationInfo("Asgarnian Ice Dungeon", new Location(3043, 9590, 0)),
                new SlayerLocationInfo("White Wolf Mountain Dungeon", new Location(2892, 9945, 0))
            ),
            "Ice giant"),
    INFERNAL_MAGES(new Task[]{new Task(VANNAKA, 8, 60, 120), new Task(CHAELDAR, 7, 110, 170)}, 40, 45, 40, "Infernal Mages are dangerous spell users, beware of their magic spells and go properly prepared.",
            List.of(
                new SlayerLocationInfo("Slayer Tower - 2nd Floor", new Location(3440, 3564, 1))
            ),
            "Infernal mage", "Malevolent mage"),
    JELLIES(new Task[]{new Task(VANNAKA, 8, 60, 120), new Task(KRYSTILIA, 5, 100, 150), new Task(CHAELDAR, 10, 110, 170), new Task(KONAR_QUO_MATEN, 6,
            120, 170, FremennikSlayerDungeon.class, CatacombsOfKourend.class)}, 50, 52, 57, "Jellies are nasty cube-like gelatinous creatures which absorb everything they come across into themselves.",
            List.of(
                new SlayerLocationInfo("Fremennik Slayer Dungeon", new Location(2716, 10031, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1715, 10039, 0))
            ),
            "Jelly", "Warped jelly", "Vitreous jelly", "Vitreous warped jelly"),
    JUNGLE_HORRORS(new Task[]{new Task(VANNAKA, 8, 60, 120), new Task(CHAELDAR, 10, 110, 170)}, 81, 1, 65, "Jungle Horrors can be found all over Mos Le'Harmless. They are strong and aggressive, so watch out!",
            List.of(
                new SlayerLocationInfo("Mos Le'Harmless", new Location(3740, 3023, 0))
            ),
            "Jungle horror"),
    KURASK(new Task[]{new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 12, 110, 170), new Task(NIEVE, 3, 120, 185),
            new Task(DURADEL, 4, 130, 200), new Task(KONAR_QUO_MATEN, 3, 120, 170, FremennikSlayerDungeon.class, IorwerthDungeon.class)}
            , 45
            , 70, 65, "Kurask are large brutal creatures with very thick hides. You'll need a Leaf-Tipped Spear, Broad Arrows, or a Magic Dart to harm it.",
            List.of(
                new SlayerLocationInfo("Fremennik Slayer Dungeon", new Location(2712, 9991, 0)),
                new SlayerLocationInfo("Iowerth Dungeon", new Location(3240, 12371, 0))
            ),
            "Kurask", "King kurask"),
    LESSER_DEMONS(new Task[]{new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 9, 110, 170), new Task(KRYSTILIA, 6,
            80, 120)}, 28, 1, 60, "Lesser Demons are magic creatures so they are weak to magical attacks. Though they're relatively weak they are still dangerous.",
            List.of(
                new SlayerLocationInfo("Taverley Dungeon", new Location(2932, 9785, 0)),
                new SlayerLocationInfo("Karamja Volcano", new Location(2835, 9622, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1700, 10063, 0)),
                new SlayerLocationInfo("Chasm of Fire", new Location(1438, 10089, 3))
            ),
            "Lesser demon"),
    /*MOLANISKS(new Task[]{
            new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 6, 110, 170)
    }, 87, 39, 50,
            "Molanisks are mole-like beings only found in dark caverns. A slayer bell is required" +
                    " to attract their attention.",
            "Molanisk"),*/
    MOSS_GIANTS(new Task[]{new Task(VANNAKA, 7, 60, 120)}, 17, 1, 40, "Like other giants, Moss Giants often wield large weapons, learn to recognise what kind of weapon it is and act accordingly.",
            List.of(
                new SlayerLocationInfo("Brimhaven Dungeon", new Location(2667, 9560, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1681, 10047, 0)),
                new SlayerLocationInfo("Varrock Sewers", new Location(3162, 9905, 0)),
                new SlayerLocationInfo("Iowerth Dungeon", new Location(3240, 12472, 0))
            ),
            "Moss giant"),
    NECHRYAEL(new Task[]{new Task(VANNAKA, 5, 60, 120), new Task(KRYSTILIA, 5, 75, 125), new Task(CHAELDAR, 12, 110, 170), new Task(NIEVE, 7, 120,
            185), new Task(DURADEL, 9, 130, 200), new Task(KONAR_QUO_MATEN, 7, 110, 110, CatacombsOfKourend.class,
            SlayerTower.class, IorwerthDungeon.class)}, 52, 80, 85, "Nechryael are demons of decay which summon small winged beings to help them fight their victims.",
            List.of(
                new SlayerLocationInfo("Slayer Tower - Basement & Top Floor", new Location(3421, 9966, 3)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1700, 10080, 0)),
                new SlayerLocationInfo("Iowerth Dungeon", new Location(3222, 12467, 0))
            ),
            Range.of("Nechs please", 200, 250), "Nechryael", "Nechryarch", "Greater Nechryael"),
    OGRES(new Task[]{new Task(VANNAKA, 7, 60, 120)}, 20, 1, 40,
            "Ogres are brutal creatures, favouring large blunt maces and clubs, they often attack without warning.",
            List.of(
                new SlayerLocationInfo("Yanille", new Location(2496, 3096, 0)),
                new SlayerLocationInfo("Combat Training Camp (No melee)", new Location(2527, 3372, 0)),
                new SlayerLocationInfo("Feldip Hills", new Location(2617, 2984, 0)),
                new SlayerLocationInfo("Myths' Guild Basement (Ogress Warriors & Shamans)", new Location(1982, 8983, 0))
            ),
            "Ogre", "Ogre chieftain", "Enclave ogre", "Reanimated ogre", "Ogress warrior", "Ogress shaman"),
    OTHERWORDLY_BEINGS(new Task[]{new Task(VANNAKA, 8, 30, 60)}, 55, 1, 40, "Otherworldly Beings are ethereal beings making them weak to magical attack.",
            List.of(
                new SlayerLocationInfo("Zanaris", new Location(2393, 4431, 0))
            ),
            "Otherworldly being"),
    /*SEA_SNAKES(new Task[] { new Task(VANNAKA, 6, 30,
            60) }, 71, 1, 50, "Sea snakes are poisonous snakes found on the island of Miscellania.", "Sea snake " +
            "hatchling", "Sea snake young", "Giant sea snake"),*/
	/*SHADOW_WARRIORS(new Task[] { new Task(VANNAKA, 8, 60, 120),
			new Task(CHAELDAR, 8, 110, 170) }, 32, 1, 60, "Shadow warriors are deceased knights in supernatural form.",
            "Shadow warrior"),*/
    SPIRITUAL_CREATURES(new Task[]{new Task(VANNAKA, 5, 60, 120), new Task(CHAELDAR, 10, 110, 170), new Task(NIEVE, 6
            , 120, 240), new Task(DURADEL, 7, 130, 200), new Task(KRYSTILIA, 6, 100, 150)}, 89, 63, 60, "Spiritual creatures can be found in the icy caverns near Trollheim, supporting the cause of their chosen god. The warriors, mages and rangers have different Slayer requirements.",
            List.of(
                new SlayerLocationInfo("God Wars Dungeon", new Location(2883, 5309, 2))
            ),
            "Spiritual ranger", "Spiritual mage", "Spiritual warrior"),
    /*TERROR_DOGS(new Task[] { new Task(VANNAKA, 6, 60,
            120) }, 86, 40, 60, "Terror dogs are terrifying dog-like beasts found in the lair of Tarn Razorlor.",
            "Terror dog"),*/
    TROLLS(new Task[]{new Task(VANNAKA, 7, 60, 120), new Task(CHAELDAR, 11, 110, 170), new Task(NIEVE, 6, 120, 185),
            new Task(DURADEL, 6, 130, 200), new Task(KONAR_QUO_MATEN, 6, 120, 170, TrollStrongholdArea.class,
            JatizsoArea.class, DeathPlateau.class)}, 18, 1, 60,
            "Trolls regenerate damage quickly but are still vulnerable to poisons, they usually use crushing weapons.",
            List.of(
                new SlayerLocationInfo("Death Plateau", new Location(2878, 3578, 0)),
                new SlayerLocationInfo("Troll Stronghold", new Location(2847, 3667, 0)),
                new SlayerLocationInfo("Jatizo (Ice Trolls)", new Location(2419, 3845, 0))
            ),
            "Mountain troll", "Troll", "Ice troll grunt", "Ice troll runt", "Ice troll male", "Ice troll female", "Ice troll", "River troll", "Troll spectator", "Thrower troll", "Stick", "Kraka", "Pee Hat", "Troll general", "Reanimated troll"),
    TUROTH(new Task[]{new Task(VANNAKA, 8, 60, 120), new Task(CHAELDAR, 10, 110, 170), new Task(NIEVE, 3, 120, 185),
            new Task(KONAR_QUO_MATEN, 3, 120, 170, FremennikSlayerDungeon.class)}, 36, 55, 60, "Turoth are large vicious creatures with thick hides. You'll need a Leaf-Tipped Spear, Broad Arrows, or a Magic Dart to harm them.",
            List.of(
                new SlayerLocationInfo("Fremennik Slayer Dungeon", new Location(2710, 10013, 0))
            ),
            "Turoth"),
    WEREWOLVES(new Task[]{new Task(VANNAKA, 7, 60, 120)}, 33, 1, 60, "Werewolves are feral creatures, they are strong and tough with sharp claws and teeth.",
            List.of(
                new SlayerLocationInfo("Canifis", new Location(3493, 3487, 0))
            ),
            "Werewolf"),
    AVIANSIES(new Task[]{new Task(CHAELDAR, 8, 110, 170), new Task(NIEVE, 6, 120, 200), new Task(DURADEL, 8, 120,
            250), new Task(KRYSTILIA, 7, 80, 150), new Task(KONAR_QUO_MATEN, 6, 120, 170, GodwarsDungeonArea.class)},
            94, 1, 0, "Aviansies are bird-like creatures found in the icy dungeons of the north. Melee weapons can't reach them, so use Magic or Ranged attacks.",
            player -> player.getSlayer().isUnlocked("Watch the Birdie"), Range.of("Birds of a feather", 130, 250),
            List.of(
                new SlayerLocationInfo("God Wars Dungeon", new Location(2884, 5310, 2))
            ),
            "Aviansie", "Kree'arra", "Flight kilisa", "Wingman skree", "Flockleader geerin", "Reanimated aviansie"),
    BLACK_DEMONS(new Task[]{new Task(CHAELDAR, 8, 110, 170), new Task(NIEVE, 9, 120, 185), new Task(DURADEL, 8, 130,
            200), new Task(KRYSTILIA, 7, 100, 150), new Task(KONAR_QUO_MATEN, 9, 120, 170, CatacombsOfKourend.class,
            ChasmOfFire.class, TaverleyDungeon.class, BrimhavenDungeon.class)}, 30, 1, 80, "Black Demons are magic creatures so they are weak to magical attacks. Though not the strongest demon, they are still dangerous.",
            List.of(
                new SlayerLocationInfo("Taverley Dungeon", new Location(2880, 9769, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1724, 10095, 0)),
                new SlayerLocationInfo("Chasm of Fire", new Location(1440, 10078, 1)),
                new SlayerLocationInfo("Brimhaven Dungeon", new Location(2696, 9493, 0))
            ),
            Range.of("It's dark in here", 200, 250), "Black demon", "Demonic gorilla", "Balfrug kreeyath", "Skotizo", "Porazdir"),
    CAVE_HORRORS(new Task[]{new Task(CHAELDAR, 10, 110, 170), new Task(NIEVE, 5, 120, 185), new Task(DURADEL, 4, 80,
            200)}, 80, 58, 85, "Cave Horrors can be found under Mos Le'Harmless. You will need a Witchwood Icon to fight them effectively.",
            List.of(
                new SlayerLocationInfo("Mos Le'Harmless (<col=ff0000>Light Source</col>)", new Location(3749, 2973, 0))
            ),
            Range.of("Horrorific", 200, 250), "Cave horror", "Cave abomination", "Reanimated horror"),
    //Kraken quantities doubled as they need to be the same as they were in OSRS due to task-requirement to slay.
    CAVE_KRAKEN(new Task[]{new Task(CHAELDAR, 12,  110, 170), new Task(NIEVE, 6, 100, 120),
            new Task(DURADEL, 9, 100, 120), new Task(KONAR_QUO_MATEN, 9, 80, 100, KrakenCove.class)}, 92, 87, 80, "Cave Kraken are found in subterranean lakes north of Eagles' Peak. They're very hard to damage with ranged or melee attacks, so try Magic.",
            List.of(
                new SlayerLocationInfo("Kraken Cove", new Location(2276, 9999, 0))
            ),
            Range.of("Krack on", 200, 300), "Cave kraken", "Kraken", "Whirlpool"),
    FOSSIL_ISLAND_WYVERN(new Task[]{new Task(CHAELDAR, 7, 110, 170), new Task(NIEVE, 5, 20, 60), new Task(DURADEL, 5,
            20, 60)}, 106, 66, 80, "Fossil Island Wyverns are extremely dangerous in different ways. To stand a good chance of surviving you'll need some elemental shielding from their icy breath.",
            player -> !player.getSlayer().isUnlocked("Stop the Wyvern") || player.getSettings().valueOf(Setting.STOP_THE_WYVERN_SLAYER_REWARD) != 0, Range.of("Wyver-nother two", 55, 75),
            List.of(
                new SlayerLocationInfo("Wyvern Cave", new Location(3603, 10229, 0)),
                new SlayerLocationInfo("Wyvern Cave (Task Only)", new Location(3598, 10290, 0))
            ),
            "Spitting wyvern", "Taloned wyvern", "Long-tailed wyvern", "Ancient wyvern"),
    GREATER_DEMONS(new Task[]{new Task(CHAELDAR, 8, 110, 170), new Task(NIEVE, 7, 120, 185), new Task(DURADEL, 9, 130
            , 200), new Task(KRYSTILIA, 8, 100, 150), new Task(KONAR_QUO_MATEN, 7, 120, 170,
            KaruulmSlayerDungeon.class, CatacombsOfKourend.class, ChasmOfFire.class, BrimhavenDungeon.class)}, 29, 1,
            75, "Greater Demons are magic creatures so they are weak to magical attacks. Though not the strongest demon, they are still dangerous.",
            List.of(
                new SlayerLocationInfo("Brimhaven Dungeon", new Location(2640, 9513, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1719, 10104, 0)),
                new SlayerLocationInfo("Karuulm Slayer Dungeon (<col=ff0000>Boots of Stone</col>)", new Location(1295, 10205, 1)),
                new SlayerLocationInfo("Chasm of Fire", new Location(1435, 10085, 2))
            ),
            Range.of("Greater challenge", 150, 200), "Tormented demon", "Greater demon", "K'ril tsutsaroth", "Tstanon Karlak", "Skotizo"),
    LIZARDMEN(new Task[]{new Task(CHAELDAR, 8, 110, 170), new Task(NIEVE, 8, 90, 120), new Task(DURADEL, 10, 130,
            210), new Task(KONAR_QUO_MATEN, 8, 90, 110, KourendBattlefront.class, LizardmanCanyon.class,
            LizardmanSettlement.class, KebosSwamp.class, MolchAndLizardmanTemple.class)}, 90, 1, 0, "Lizardmen have a poisonous bite. Their shamans have many peculiar tricks - make sure they don't jump on your head.", player -> player.getSlayer().isUnlocked(
            "Reptile got ripped"), null,
            List.of(
                new SlayerLocationInfo("Lizardman Canyon", new Location(1507, 3679, 0)),
                new SlayerLocationInfo("Kebos Swamp", new Location(1300, 3615, 0)),
                new SlayerLocationInfo("Kourend Battlefront", new Location(1337, 3711, 0)),
                new SlayerLocationInfo("izardman Temple (Shamans)", new Location(1293, 10091, 0)),
                new SlayerLocationInfo("Lizardman Canyon (Shamans)", new Location(1455, 3690, 0)),
                new SlayerLocationInfo("Lizardman Settlement/Caves (Reg & Shamans)", new Location(1314, 3570, 0))
            ),
            "Lizardman", "Lizardman shaman", "Lizardman brute"),
    MUTATED_ZYGOMITES(new Task[]{new Task(CHAELDAR, 7, 60, 120), new Task(NIEVE, 2, 11, 86), new Task(DURADEL, 2, 20,
            30), new Task(KONAR_QUO_MATEN, 2, 10, 25, ZanarisArea.class, FossilIsland.class)}, 74, 57, 60, "Mutated Zygomites are hard to destroy. They regenerate quickly so you will need to finish them with fungicide.",
            List.of(
                new SlayerLocationInfo("Zanaris", new Location(2418, 4373, 0)),
                new SlayerLocationInfo("Fossil Island (Ancient Zygomites)", new Location(3681, 3866, 0))
            ),
            "Zygomite", "Ancient zygomite", "Mutated zygomite"),
    IRON_DRAGONS(new Task[]{new Task(CHAELDAR, 12, 60, 120), new Task(NIEVE, 5, 25, 60), new Task(DURADEL, 5, 40, 60)
            , new Task(KONAR_QUO_MATEN, 5, 30, 50, CatacombsOfKourend.class, BrimhavenDungeon.class)}, 59, 1, 80,
            "Iron Dragons are some of the weaker metallic dragons, their iron scales are far thicker than normal iron armour.",
            List.of(
                    new SlayerLocationInfo("Brimhaven Dungeon", new Location(2710, 9474, 0)),
                    new SlayerLocationInfo("Catacombs of Kourend", new Location(1662, 10076, 0))
            ),
            Range.of("Pedal to the metals", 60, 100), "Iron dragon"),
    SKELETAL_WYVERNS(new Task[]{new Task(CHAELDAR, 7, 110, 170), new Task(NIEVE, 5, 3, 70), new Task(DURADEL, 7, 20,
            50), new Task(KONAR_QUO_MATEN, 9, 50, 70, AsgarnianIceDungeon.class)}, 72, 72, 70, "Skeletal Wyverns are extremely dangerous and they are hard to hit with arrows as they slip right through. To stand a good chance of surviving you'll need some elemental shielding from its icy breath.",
            List.of(
                new SlayerLocationInfo("Asgarnian Ice Dungeon", new Location(3056, 9562, 0))
            ),
            Range.of("Wyver-nother one", 50, 70), "Skeletal wyvern"),
    STEEL_DRAGONS(new Task[]{new Task(CHAELDAR, 9, 110, 170), new Task(NIEVE, 5, 30, 110), new Task(DURADEL, 7, 10,
            20), new Task(KONAR_QUO_MATEN, 5, 30, 50, CatacombsOfKourend.class, BrimhavenDungeon.class)}, 60, 1, 85,
            "Steel Dragons are dangerous metallic dragons, their steel scales are far thicker than normal steel armour.",
            List.of(
                new SlayerLocationInfo("Brimhaven Dungeon", new Location(2710, 9474, 0)),
                new SlayerLocationInfo("Catacombs of Kourend", new Location(1617, 10051, 0))
            ),
            Range.of("Pedal to the metals", 40, 60), "Steel dragon"),
    TZHAAR(new Task[]{new Task(CHAELDAR, 8, 110, 170), new Task(NIEVE, 10, 130, 200),
            new Task(DURADEL, 10, 130, 200)}, 96, 1, 0, "Tzhaar reside in their cave under the Karamja volcano. Beware as they can call to their fellows for aid.", player -> player.getSlayer().isUnlocked("Hot stuff"), null,
            List.of(
                new SlayerLocationInfo("TzHaar City", new Location(2457, 5158, 0))
            ),
            "TzHaar-Ket", "TzHaar-Xil", "TzHaar-Mej", "TzHaar-Hur", "Tz-Kih", "Tz-Kek", "Tok-Xil", "Yt-MejKot", "Ket-Zek", "TzTok-Jad", "Yt-HurKot", "Jal-Nib",
            "Jal-MejRah", "Jal-Ak", "Jal-ImKot", "Jal-Xil", "Jal-Zek", "JalTok-Jad", "TzKal-Zuk", "Jal-MejJak", "Reanimated TzHaar") {
        @Override
        public boolean validate(final String name, final NPC npc) {
            RegionArea location = GlobalAreaManager.getArea(npc.getLocation());
            if (location instanceof Inferno || location instanceof FightCaves) {
                return false;
            }
            return super.validate(name, npc);
        }
    },
    BLACK_DRAGONS(new Task[]{new Task(NIEVE, 6, 10, 40), new Task(DURADEL, 9, 10, 20), new Task(KRYSTILIA, 7, 5, 20),
            new Task(KONAR_QUO_MATEN, 6, 10, 15, CatacombsOfKourend.class, MythsGuildBasement.class,
                    EvilChickenLair.class, TaverleyDungeon.class)}, 27, 1, 80, "Black Dragons are the strongest dragons and very fierce, watch out for their fiery breath. Those with higher slayer knowledge and who seek a much tougher, but substantially more rewarding foe, may wish to fight Brutal versions of these creatures in the Kourend Catacombs.",
            List.of(
                new SlayerLocationInfo("Taverley Dungeon", new Location(2836, 9819, 0)),
                new SlayerLocationInfo("Evil Chicken's Lair", new Location(2461, 4356, 0)),
                new SlayerLocationInfo("Myths' Guild Basement", new Location(1944, 8974, 1)),
                new SlayerLocationInfo("Taverley Dungeon (Baby Black Dragons)", new Location(2867, 9827, 1)),
                new SlayerLocationInfo("Catacombs of Kourend (Brutal Black Dragons)", new Location(1664, 10049, 0))
            ),
            Range.of("Fire & Darkness", 40, 60), "Black dragon", "Baby black dragon", "King black dragon", "Brutal black dragon",
            1871, 1872, 7955),
    DARK_BEASTS(new Task[]{new Task(NIEVE, 5, 10, 20), new Task(DURADEL, 11, 10, 20), new Task(KONAR_QUO_MATEN, 8, 10
            , 15, MournerTunnels.class, IorwerthDungeon.class)}, 66, 90, 90, "Dark Beasts are large, dog-like predators. Their massively muscled bodies protect them from crushing weapons.",
            List.of(
                new SlayerLocationInfo("Mourner Tunnels", new Location(2006, 4643, 0)),
                new SlayerLocationInfo("Iorwerth Dungeon", new Location(3178, 12422, 0))
            ),
            Range.of("Need more darkness", 100, 149), "Dark beast", "Night beast"),
    MITHRIL_DRAGONS(new Task[]{new Task(NIEVE, 5, 4, 9), new Task(DURADEL, 10, 5, 10), new Task(KONAR_QUO_MATEN, 5, 3
            , 6, AncientCavern.class)}, 93, 1, 0,
            "Mithril dragons are more vulnerable to magic and to stab-based melee attacks than to anything else.", player -> player.getSlayer().isUnlocked("I hope you " +
            "mith me"), Range.of("I really mith you", 20, 40),
            List.of(
                new SlayerLocationInfo("Ancient Cavern", new Location(1774, 5349, 0))
            ),
            "Mithril dragon"),
    RED_DRAGONS(new Task[]{new Task(NIEVE, 5, 30, 80), new Task(DURADEL, 8, 30, 65), new Task(KONAR_QUO_MATEN, 5, 120
            , 170, BrimhavenDungeon.class, CatacombsOfKourend.class, MythsGuildBasement.class)
    }, 26, 1, 68, "Red Dragons are very powerful, stronger than most dragons, watch out for their fiery breath.",
            player -> player.getSlayer().isUnlocked("Seeing red"), null,
            List.of(
                new SlayerLocationInfo("Brimhaven Dungeon", new Location(2687, 9506, 0)),
                new SlayerLocationInfo("Catacombs of Kourend (Brutal Red Dragons)", new Location(1642, 10073, 0)),
                new SlayerLocationInfo("Myths' Guild Basement", new Location(1927, 9009, 1))
            ),
            "Red dragon", "Baby red dragon", "Brutal red dragon", 244, 245, 246),
    /*MINIONS_OF_SCABARAS(new Task[] { new Task(NIEVE, 4, 30,
            60) }, 85, 1, 0, "Scabarites are ancient egiptian creatures. Their bite can be very dangerous. They're " +
            "found in the Sophanem dungeon.", Range
                    .of("Get scabaright on it", 130, 170), "Scarabs", "Scarab swarms", "Locust riders", "Scarab
                    mages", "Giant scarab"),*/
    SMOKE_DEVILS(new Task[]{new Task(NIEVE, 7, 110, 185), new Task(DURADEL, 9, 130, 200), new Task(KONAR_QUO_MATEN, 7
            , 120, 170, YanilleUndergroundArea.class)}, 95, 93, 85, "Smoke Devils emit a lot of noxious gases & are slow and lumbering. You'll need a facemask in order to get anywhere near them.",
            List.of(
                new SlayerLocationInfo("Smoke Devil Dungeon", new Location(3207, 9377, 0))
            ),
            "Smoke devil", "Thermonuclear smoke devil", "Nuclear smoke devil"),
    SUQAHS(new Task[]{new Task(NIEVE, 8, 130, 185), new Task(DURADEL, 8, 60, 90)}, 83, 1, 85, "Suqahs can only be found on the mystical Lunar Isle. They are capable of melee and magic attacks and often drop hide, teeth and herbs!",
            List.of(
                new SlayerLocationInfo("Lunar Isle", new Location(2111, 3917, 0))
            ),
            Range.of("Suq-a-nother one", 185, 250), "Suqah"),
    WATERFIENDS(new Task[]{new Task(DURADEL, 2, 130, 200), new Task(KONAR_QUO_MATEN, 2, 120, 170, AncientCavern.class
            , KrakenCove.class, IorwerthDungeon.class)}, 88, 1, 75, "Waterfiends are creatures of water, which live under the Baxtorian Lake. Their watery form is well defended against slashing and piercing weapons, so use something blunt.",
            List.of(
                new SlayerLocationInfo("Ancient Cavern", new Location(1769, 5358, 0)),
                new SlayerLocationInfo("Kraken Cove", new Location(2283, 9994, 0)),
                new SlayerLocationInfo("Iorweth Dungeon", new Location(3200, 12461, 0))
            ),
            "Waterfiend"),
    BANDITS(new Task[]{new Task(KRYSTILIA, 6, 75, 125)}, 102, 1, 0, "Bandits band together for protection against enemies such as yourself.",
            List.of(
                new SlayerLocationInfo("<col=ff0000>Bandit Camp</col>", new Location(3038, 3651, 0))
            ),
            "Bandit", "Guard bandit", "Black Heather", "Speedy Keith", "Donny the lad"),
    DARK_WARRIORS(new Task[]{new Task(KRYSTILIA, 6, 70, 125)}, 103, 1, 0, "Dark Warriors were protective armour. If you want to fight them with melee, try crush-based attacks. Or try magic instead.",
            List.of(
                new SlayerLocationInfo("<col=ff0000>Dark Warriors' Fortress (Bandit Camp)</col>", new Location(3038, 3651, 0))
            ),
            "Dark warrior"),
    ENTS(new Task[]{new Task(KRYSTILIA, 6, 35, 60)}, 101, 1, 0, "Ents are walking tree-people. Bring a hatchet to chop their wood once you've slain them.",
            List.of(
                new SlayerLocationInfo("<col=ff0000>Chaos Temple</col>>", new Location(3237, 3635, 0))
            ),
            "Ent"),
    LAVA_DRAGONS(new Task[]{new Task(KRYSTILIA, 6, 35, 60)}, 104, 1, 0, "Lava dragons live in a volcanic area of the Wilderness. Their bones are well worth burying for the Prayer XP.",
            List.of(
                new SlayerLocationInfo("<col=ff0000>Lava Dragon Isle (Black Chinchompas)</col>", new Location(3142, 3770, 0))
            ),
            "Lava dragon"),
    MAGIC_AXES(new Task[]{new Task(KRYSTILIA, 7, 70, 125)}, 91, 1, 0, "Magic Axes have a mind of their own. You may find magic or slash-based melee attacks are slightly more effective against them.",
            List.of(
                new SlayerLocationInfo("<col=ff0000>Axe Hut (Edgeville Lever)</col>", new Location(3153, 3923, 0))
            ),
            "Magic axe"),
    MAMMOTHS(new Task[]{new Task(KRYSTILIA, 6, 75, 125)}, 99, 1, 0, "Mammoths are large beautiful creatures found in " +
            "the wilderness.",
            List.of(
                new SlayerLocationInfo("South of Ferox Enclave", new Location(3134, 3617, 0))
            ),
            "Mammoth"),
    ROGUES(new Task[]{new Task(KRYSTILIA, 5, 75, 125)}, 100, 1, 0, "Rogues are humans gone rogue. They can be found " +
            "deep in the Wilderness.",
            List.of(
                new SlayerLocationInfo("<col=ff0000>Rogues' Castle (Edgeville Lever)</col>", new Location(3153, 3923, 0))
            ),
            "Rogue"),
    SPIRITUAL_MAGE(new Task[]{new Task(KRYSTILIA, 6, 130, 200)}, 89, 83, 0, "Spiritual mages are strong mages found " +
            "in the godwars dungeon.",
            List.of(
                new SlayerLocationInfo("<col=ff0000>God Wars Dungeon (Lava Maze)</col>", new Location(3029, 3841, 2))
            ),
            Range.of("Spiritual fervour", 181, 250), "Spiritual mage"),
    REVENANTS(new Task[]{new Task(KRYSTILIA, 5, 40, 100)}, 107, 1, 30, "Revenants are the ghostly versions of " +
            "creatures slain during the God Wars. You may wish to protect yourself with a bracelet of ethereum.",
            List.of(
                new SlayerLocationInfo("<col=ff0000>Revenant Caves (Revanants (17))</col>", new Location(3081, 3653, 0))
            ),
            "Revenant imp", "Revenant goblin", "Revenant pyrefiend", "Revenant hobgoblin", "Revenant cyclops",
            "Revenant hellhound", "Revenant demon", "Revenant ork", "Revenant dark beast", "Revenant knight",
            "Revenant dragon"),
    VAMPYRE(new Task[]{
            new Task(MAZCHNA, 5, 10, 20),
            new Task(VANNAKA, 5, 10, 20),
            new Task(CHAELDAR, 5, 80, 120),
            new Task(KONAR_QUO_MATEN, 5, 100, 160, Darkmeyer.class, Meiyerditch.class),
            new Task(NIEVE, 5, 110, 170),
            new Task(DURADEL, 5, 100, 210),
    }, 109, 0, 35, "",
            List.of(
                new SlayerLocationInfo("Darkmeyer", new Location(3603, 3366, 0)),
                new SlayerLocationInfo("Meiyerditch", new Location(3629, 3198, 0)),
                new SlayerLocationInfo("Vanstrom Klause", new Location(3570, 3358, 0))
            ),
            "Vampyre Juvinate", "Vampyre Juvenile", "Vyre", "Vyrewatch Sentinel", "Vyrewatch", "Feral Vampyre", "Vanstrom Klause"),

    ADAMANT_DRAGONS(new Task[]{new Task(NIEVE, 2, 3, 7), new Task(DURADEL, 2, 4, 9), new Task(KONAR_QUO_MATEN, 5, 3,
            6, LithkrenVault.class)}, 108, 1, 0, "Adamant dragons are metallic dragons created by Zorgoth. You may " +
            "wish to protect yourself against their powerful dragonfire.",
            List.of(
                new SlayerLocationInfo("Lithkren Vault", new Location(1563, 5074, 0))
            ),
            Range.of("Ada'mind some more", 20, 30), "Adamant dragon"),
    RUNE_DRAGONS(new Task[]{new Task(NIEVE, 2, 3, 6), new Task(DURADEL, 2, 3, 8), new Task(KONAR_QUO_MATEN, 5, 3, 6,
            LithkrenVault.class)}, 109, 1, 0, "Rune dragons are metallic dragons created by Zorgoth. You may wish to " +
            "protect yourself against their powerful dragonfire.",
            List.of(
                new SlayerLocationInfo("Lithkren Vault", new Location(1573, 5074, 0))
            ),
            Range.of("RUUUUUNE", 30, 60), "Rune dragon"),
    CHAOS_DRUIDS(new Task[]{new Task(KRYSTILIA, 5, 50, 85)}, 110, 1, 0, "Chaos druids are followers of Guthix and " +
            "zamorak. I've found that ranging them works the best.",
            List.of(
                new SlayerLocationInfo("<col=ff0000>Chaos Temple</col>", new Location(3235, 3638, 0))
            ),
            "Chaos druid", "Elder chaos druid", "Reanimated chaos druid"),
    DRAKES(new Task[]{new Task(KONAR_QUO_MATEN, 10, 125, 140, KaruulmSlayerDungeon.class)}, 112, 84, 0, "Drakes are " +
            "wingless dragons found in the middle level of the Karuulm Slayer Dungeon in Mount Karuulm.",
            List.of(
                new SlayerLocationInfo("Karuulm Slayer Dungeon (<col=ff0000>Boots of Stone</col>)", new Location(1312, 10222, 1))
            ),
            "Drake"),
    HYDRAS(new Task[]{new Task(KONAR_QUO_MATEN, 10, 125, 190, KaruulmSlayerDungeon.class)}, 113, 95, 0, "Hydras are " +
            "draconic creatures found in the lower level of the Karuulm Slayer Dungeon in Mount Karuulm.",
            List.of(
                new SlayerLocationInfo("Karuulm Slayer Dungeon (<col=ff0000>Boots of Stone</col>)", new Location(1311, 10217, 0))
            ),
            "Hydra", "Alchemical Hydra") {
        @Override
        public float getExperience(final NPC npc) {
            if (npc instanceof AlchemicalHydra) {
                return 1320;
            }
            return npc.getMaxHitpoints() * (npc instanceof SuperiorNPC ? 10 : 1);
        }
    },
    WYRMS(new Task[]{new Task(KONAR_QUO_MATEN, 10, 125, 190, KaruulmSlayerDungeon.class)}, 111, 62, 0, "Wyrms are " +
            "draconic creatures found in the lower level of the Karuulm Slayer Dungeon in Mount Karuulm.",
            List.of(
                new SlayerLocationInfo("Karuulm Slayer Dungeon (<col=ff0000>Boots of Stone</col>)", new Location(1287, 10199, 0))
            ),
            "Wyrm"),
    TZTOK_JAD(new Task[0], 97, 1, 0, "TzTok-Jad is a fierce monster found at the end of the Fight Caves.",
            List.of(
                new SlayerLocationInfo("Fight Caves", new Location(2438, 5169, 0))
            ),
            "Tz-Kih", "Tz-Kek", "Tok-Xil", "Yt-MejKot", "Ket-Zek", "TzTok-Jad") {
        @Override
        public float getExperience(final NPC npc) {
            if (npc instanceof TzTokJad) {
                return 25250;
            }
            return super.getExperience(npc);
        }

        @Override
        public boolean validate(final String name, final NPC npc) {
            if (!(GlobalAreaManager.getArea(npc.getLocation()) instanceof FightCaves)) {
                return false;
            }
            return super.validate(name, npc);
        }

        @Override
        public String toString() {
            return "TzTok-Jad";
        }
    },
    TZKAL_ZUK(new Task[0], 105, 1, 0, "TzKal-Zuk is a fierce monster found at the end of the Inferno.",
            List.of(
                new SlayerLocationInfo("Inferno", new Location(2496, 5113, 0))
            ),
            "TzKal-Zuk", "Jal-Nib", "Jal-MejRah", "Jal-Ak", "Jal-AkRek-Mej", "Jal-AkRek-Xil", "Jal-AkRek-Ket", "Jal-ImKot", "Jal-Xil", "Jal-Zek", "JalTok-Jad", "Yt-HurKot", "Yt-HurKot", "Jal-MejJak") {
        @Override
        public float getExperience(final NPC npc) {
            if (npc instanceof TzKalZuk) {
                return 101890;
            }
            return super.getExperience(npc);
        }

        @Override
        public boolean validate(final String name, final NPC npc) {
            RegionArea location = GlobalAreaManager.getArea(npc.getLocation());
            if (!(location instanceof Inferno inferno)) {
                return false;
            }
            if (inferno.isPracticeMode()) {
                return false;
            }
            return super.validate(name, npc);
        }

        @Override
        public String toString() {
            return "TzKal-Zuk";
        }
    };

    private final Task[] taskSet;
    private final int slayerRequirement;
    private final int taskId;
    private final int combatRequirement;
    private final String[] monsters;
    private final int[] monsterIds;
    private final String tip;
    private final Predicate<Player> predicate;
    private final Range extendedRange;

    // Location data for slayer tips and teleports
    private final List<SlayerLocationInfo> locations;

    public static final RegularTask[] VALUES = values();

    RegularTask(@NotNull final Task[] taskSet, final int taskId, final int slayerRequirement,
                final int combatRequirement, final String tip, final Object... monsters) {
        this(taskSet, taskId, slayerRequirement, combatRequirement, tip, null, null, null, monsters);
    }

    RegularTask(@NotNull final Task[] taskSet, final int taskId, final int slayerRequirement,
                final int combatRequirement, final String tip, final Range range, final Object... monsters) {
        this(taskSet, taskId, slayerRequirement, combatRequirement, tip, null, range, null, monsters);
    }

    RegularTask(@NotNull final Task[] taskSet, final int taskId, final int slayerRequirement,
                final int combatRequirement, final String tip, final Predicate<Player> predicate,
                final Object... monsters) {
        this(taskSet, taskId, slayerRequirement, combatRequirement, tip, predicate, null, null, monsters);
    }

    RegularTask(@NotNull final Task[] taskSet, final int taskId, final int slayerRequirement,
                final int combatRequirement, final String tip, final Predicate<Player> predicate,
                final Range extendedRange, final Object... monsters) {
        this(taskSet, taskId, slayerRequirement, combatRequirement, tip, predicate, extendedRange, null, monsters);
    }

    RegularTask(@NotNull final Task[] taskSet, final int taskId, final int slayerRequirement,
                final int combatRequirement, final String tip, final List<SlayerLocationInfo> locations,
                final Object... monsters) {
        this(taskSet, taskId, slayerRequirement, combatRequirement, tip, null, null, locations, monsters);
    }

    RegularTask(@NotNull final Task[] taskSet, final int taskId, final int slayerRequirement,
                final int combatRequirement, final String tip, final List<SlayerLocationInfo> locations,
                final Range extendedRange, final Object... monsters) {
        this(taskSet, taskId, slayerRequirement, combatRequirement, tip, null, extendedRange, locations, monsters);
    }

    RegularTask(@NotNull final Task[] taskSet, final int taskId, final int slayerRequirement,
                final int combatRequirement, final String tip, final Predicate<Player> predicate,
                final Range extendedRange, final List<SlayerLocationInfo> locations, final Object... monsters) {
        this.taskSet = taskSet;
        this.taskId = taskId;
        this.combatRequirement = combatRequirement;
        this.slayerRequirement = slayerRequirement;
        this.tip = tip;
        this.predicate = predicate;
        this.extendedRange = extendedRange;
        this.locations = locations != null ? locations : new ArrayList<>();
        final ObjectList<String> npcs = new ObjectArrayList<>(monsters.length);
        final IntArrayList intArray = new IntArrayList(monsters.length);
        for (final Object monster : monsters) {
            if (monster instanceof String) {
                npcs.add(((String) monster).toLowerCase());
            } else {
                intArray.add((int) monster);
            }
        }
        this.monsterIds = intArray.toIntArray();
        this.monsters = npcs.toArray(new String[0]);
    }

    public final Task getCertainTaskSet(final SlayerMaster slayerMaster) {
        for (final Task set : taskSet) {
            if (set.getSlayerMaster() == slayerMaster) {
                return set;
            }
        }
        return null;
    }

    public final String getSingularName() {
        if (equals(TZHAAR)) {
            return "TzHaar";
        }
        if (equals(DWARVES)) {
            return "Dwarf";
        }
        if (equals(WOLVES)) {
            return "Wolf";
        }
        if (equals(WEREWOLVES)) {
            return "Werewolf";
        }
        String name = name().toLowerCase().replace("_", " ");
        if (name.charAt(name.length() - 1) == 's') {
            name = name.substring(0, name.length() - 1);
        }
        return name.substring(0, 1).toUpperCase() + name.substring(1);
    }

    public static String isAssignable(final String name) {
        final String lowercaseName = name.toLowerCase();
        final BossTask bossTask = BossTask.MAPPED_VALUES.get(lowercaseName);
        if (bossTask != null) {
            return StringFormatUtil.formatString(bossTask.getTaskName());
        }
        for (final RegularTask assignable : VALUES) {
            for (final String matchingName : assignable.monsters) {
                if (matchingName.equals(name)) {
                    return assignable.getSingularName();
                }
            }
        }
        return null;
    }

    @Override
    public String toString() {
        if (equals(TZHAAR)) {
            return "TzHaar";
        }
        final String name = name().toLowerCase().replace("_", " ");
        return name.substring(0, 1).toUpperCase() + name.substring(1);
    }

    @Override
    public boolean validate(final String name, final NPC npc) {
        if (ArrayUtils.contains(this.monsterIds, npc.getId())) {
            return true;
        }
        for (final String match : monsters) {
            if (name.equalsIgnoreCase(match)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public float getExperience(final NPC npc) {
        final BossTask bossAssignment = BossTask.MAPPED_VALUES.get(npc.getDefinitions().getName().toLowerCase());
        if (bossAssignment != null) {
            return bossAssignment.getXp();
        }
        return npc.getMaxHitpoints() * (npc instanceof SuperiorNPC ? 10 : 1);
    }

    public static final class Range {
        private final String extensionName;
        private final int min;
        private final int max;

        public static Range of(final String extensionName, final int min, final int max) {
            return new Range(extensionName, min, max);
        }

        public Range(String extensionName, int min, int max) {
            this.extensionName = extensionName;
            this.min = min;
            this.max = max;
        }

        public String getExtensionName() {
            return extensionName;
        }

        public int getMin() {
            return min;
        }

        public int getMax() {
            return max;
        }
    }

    @Override
    public String getTaskName() {
        return toString();
    }

    @Override
    public String getEnumName() {
        return name();
    }

    public Task[] getTaskSet() {
        return taskSet;
    }

    public boolean hasTaskWith(SlayerMaster slayerMaster) {
        for (Task task : taskSet) {
            if (task.getSlayerMaster() == slayerMaster) {
                return true;
            }
        }
        return false;
    }

    public int getSlayerRequirement() {
        return slayerRequirement;
    }

    public int getTaskId() {
        return taskId;
    }

    public int getCombatRequirement() {
        return combatRequirement;
    }

    public String[] getMonsters() {
        return monsters;
    }

    public int[] getMonsterIds() {
        return monsterIds;
    }

    public String getTip() {
        return tip;
    }

    public Predicate<Player> getPredicate() {
        return predicate;
    }

    public Range getExtendedRange() {
        return extendedRange;
    }

    public List<SlayerLocationInfo> getLocations() {
        return locations;
    }

    /**
     * Data class to hold location information for slayer tasks
     */
    public static class SlayerLocationInfo {
        private final String locationName;
        private final Location teleportLocation;

        public SlayerLocationInfo(String locationName, Location teleportLocation) {
            this.locationName = locationName;
            this.teleportLocation = teleportLocation;
        }

        public String getLocationName() { return locationName; }
        public Location getTeleportLocation() { return teleportLocation; }
    }

}
