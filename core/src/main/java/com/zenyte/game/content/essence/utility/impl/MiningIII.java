package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> (Discord: Kryptexxical)
 */
public class MiningIII extends EssencePerk {

    public static boolean roll() {
        return Utils.random(99) < 50;
    }
    public static boolean rollBank() { return Utils.random(99) < 30; }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_MiningIII;
    }

    @Override
    public String name() {
        return "Mining (III)";
    }

    @Override
    public String description() {
        return "Increases your double ore chance while mining to 50%, in addition to your 30% chance to bank resources.";
    }
    @Override
    public boolean purchasable(Player p) {
        return p.hasUtilityPerk(MiningII.class);
    }

    @Override
    public String purchasableErrorMessage(Player player) {
        return "You must unlock the Mining (II) perk before you can purchase the Mining (III) perk.";
    }
    @Override
    public int item() {
        return 447;
    }

}
