package com.zenyte.game.content.worldevent.voteboss;

import com.google.common.eventbus.Subscribe;
import com.near_reality.tools.discord.community.DiscordBroadcastKt;
import com.near_reality.tools.discord.community.DiscordCommunityBot;
import com.zenyte.game.content.worldboost.WorldBoost;
import com.zenyte.game.content.worldboost.type.WorldBossBoost;
import com.zenyte.game.task.WorldTask;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.World;
import com.zenyte.game.world.broadcasts.BroadcastType;
import com.zenyte.game.world.broadcasts.WorldBroadcasts;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.plugins.events.ServerLaunchEvent;
import com.zenyte.utils.TimeUnit;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class GalvekHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GalvekHandler.class);

    private WorldTask galvekSpawnTask;

    private int votes;

    private boolean enabled = true;

    private final Galvek galvek = new Galvek();

    public static GalvekHandler get() {
        return instance;
    }

    private int announcements;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public void addVotes(Player player, int amt) {
        if(!enabled)
            return;

        votes +=amt;

        if(votes >= GalvekConstants.AMT_TO_SPAWN) {
            votes = 0;
            announcements = 0;
            if (galvekSpawnTask != null) {
                LOGGER.warn("Cancelling previous vote boss spawn task in favor of new one.");
                WorldTasksManager.stop(galvekSpawnTask);
            }
            galvekSpawnTask = () -> {
                WorldBroadcasts.sendMessage("<img=85> The Vote Boss has spawned at ::vb", BroadcastType.VOTE_BOSS, true);
                galvek.start();
            };
            WorldBroadcasts.sendMessage("<img=85> The Vote Boss will spawn in 60 seconds at ::vb", BroadcastType.VOTE_BOSS, true);
            WorldTasksManager.schedule(galvekSpawnTask, (int) TimeUnit.SECONDS.toTicks(GalvekConstants.GALVEK_SPAWN_DELAY_IN_SECONDS));
        } else {

            if(votes >= 24 && announcements == 0 || votes >= 44 && announcements == 1) {
                int announcedAmount = switch (announcements) {
                    case 0 -> 25;
                    case 1 -> 5;
                    default -> 0;
                };
                WorldBroadcasts.sendMessage("<img=" + 85 + "><col=" + "e59400" + ">" + "<shad=000000>" + "Event: " + player.getName() + " has claimed " + amt + " votes, " + announcedAmount + " more votes until the Vote Boss spawns!", BroadcastType.VOTE_BOSS, false);
                DiscordBroadcastKt.onVotesMilestone(DiscordCommunityBot.INSTANCE, player, amt, amtTillSpawn());
                announcements++;
            }
        }
    }

    public Galvek getGalvek() {
        return galvek;
    }

    public int amtTillSpawn() {
        return GalvekConstants.AMT_TO_SPAWN - votes;
    }


    private static GalvekHandler instance;


    public void activateRandomInactiveWorldBoost() {
        if(!enabled) {
            return;
        }

        List<WorldBossBoost> availableBoosts = new ArrayList<>(List.of(WorldBossBoost.VALUES));
        availableBoosts.removeIf(World::hasBoost);

        WorldBossBoost random = Utils.random(availableBoosts);
        if (random == null) {
            return;
        }

        activateBoost(random);
    }

    public static void activateBoost(WorldBossBoost boost) {
        activateBoost(boost, GalvekConstants.BOOST_HOURS);
    }

    public static void activateBoost(WorldBossBoost boost, int hours) {
        activateBoost(null, boost, hours);
    }

    public static void activateBoost(@Nullable Player activator, WorldBossBoost boost, int hours) {
        World.getWorldBoosts().stream()
                .filter(b -> b.getBoostType() == boost)
                .findAny()
                .ifPresentOrElse(
                        existingBoost -> existingBoost.extend(activator, 1, true),
                    () -> {
                        final long endTime = System.currentTimeMillis() + TimeUnit.HOURS.toMillis(hours);
                        WorldBoost worldBoost = new WorldBoost(boost, endTime, hours);
                        worldBoost.activate(activator, true);
                    }
                );
    }

    public static void activateBoost(WorldBossBoost boost, int hours, boolean announce) {
        long endTime = System.currentTimeMillis() + TimeUnit.HOURS.toMillis(hours);
        WorldBoost worldBoost = new WorldBoost(boost, endTime, hours);
        worldBoost.activate(announce);
    }

    private void start() {
        WorldTasksManager.schedule(() -> World.spawnNPC(galvek));
    }

    @Subscribe
    public static void onServerLaunch(ServerLaunchEvent event) {
        instance = new GalvekHandler();
        instance.start();
    }

    @SuppressWarnings("ConstantValue")
    public boolean isGalvekActive() {
        return galvek != null && galvek.fightStarted;
    }
}
