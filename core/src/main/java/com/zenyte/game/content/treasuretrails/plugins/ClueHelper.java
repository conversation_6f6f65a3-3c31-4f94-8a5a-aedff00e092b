package com.zenyte.game.content.treasuretrails.plugins;

import com.zenyte.game.item.Item;
import com.zenyte.game.world.entity.player.Player;
import mgi.types.config.items.ItemDefinitions;

public final class ClueHelper {

    /**
     * Checks if the player already has a clue scroll of the same difficulty in inventory.
     * Returns true if blocked and sends the appropriate message.
     */
    public static boolean hasClueConflict(Player player, int clueId) {
        final String clueName = ItemDefinitions.getOrThrow(clueId).getName().toLowerCase();

        for (Item invItem : player.getInventory().getContainer().getItems().values()) {
            if (invItem == null) continue;

            final String invName = ItemDefinitions.getOrThrow(invItem.getId()).getName().toLowerCase();
            if (invName.equals(clueName)) {
                player.sendMessage("You can only have one " + clueName + " at a time.");
                return true;
            }
        }
        return false;
    }
}
