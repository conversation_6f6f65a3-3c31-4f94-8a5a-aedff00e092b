package com.zenyte.game.content.essence.utility;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.utility.impl.*;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;

import java.util.HashMap;
import java.util.Map;

public enum UtilityPerkWrapper {
    /*
     * IMPORTANT: IDs are now explicit and stable.
     * You can reorder this enum or display them sorted however you want,
     * IDs will never shift.
     */

    BarbarianFisher(1, BarbarianFisher.class),
    BoneCruncher(2, BoneCruncher.class),
    BountifulSacrifice(3, BountifulSacrifice.class),
    MasterOfTheCraft(4, MasterOfTheCraft.class),
    FirstImpressions(5, FirstImpressions.class),
    RunForrestRun(6, RunForrestRun.class),
    IWantItAll(7, IWantItAll.class),
    SousChef(8, SousChef.class),
    <PERSON><PERSON>(9, Woodsman.class),
    <PERSON><PERSON>ting<PERSON>(10, Wood<PERSON>tingI.class),
    Pyromaniac(11, Pyromaniac.class),
    Double<PERSON>yr<PERSON>(12, DoublePyro.class),
    Mixologist(13, Mixologist.class),
    Miner<PERSON>ortyNiner(14, MinerFortyNiner.class),
    MiningI(15, MiningI.class),
    Botanist(16, Botanist.class),
    TrackStar(17, TrackStar.class),
    SwissArmyMan(18, SwissArmyMan.class),
    SleightOfHand(19, SleightOfHand.class),
    BetterThief(20, BetterThief.class),
    Alchoholic(21, Alchoholic.class),
    AshesToAshes(22, AshesToAshes.class),
    DoubleChins(23, DoubleChins.class),
    FarmersFortune(24, FarmersFortune.class),
    Fertilizer(25, Fertilizer.class),
    TheLegendaryFisherman(26, TheLegendaryFisherman.class),
    FishingI(27, FishingI.class),
    ClueSkipper(28, ClueSkipper.class),
    ClueStepMinimizer(29, ClueStepMinimizer.class),
    ClueCollector(30, ClueCollector.class),
    UnholyIntervention(31, UnholyIntervention.class),
    SlayersFavor(32, SlayersFavor.class),
    ContractKiller(33, ContractKiller.class),
    IgnoranceIsBliss(34, IgnoranceIsBliss.class),
    InfallibleShackles(35, InfallibleShackles.class),
    BrawnOfJustice(36, BrawnOfJustice.class),
    VigourOfInquisition(37, VigourOfInquisition.class),
    TheRedeemer(38, TheRedeemer.class),
    HoleyMoley(39, HoleyMoley.class),
    SustainedAggression(40, SustainedAggression.class),
    ArcaneKnowledge(41, ArcaneKnowledge.class),
    BurnBabyBurn(42, BurnBabyBurn.class),
    Backfire(43, Backfire.class),
    IVotedI(44, IVotedI.class),
    Locksmith(45, Locksmith.class),
    DoubleTap(46, DoubleTap.class),
    CorporealScrutiny(47, CorporealScrutiny.class),
    HoarderMentality(48, HoarderMentality.class),
    IceForTheEyeless(49, IceForTheEyeless.class),
    DagannothPeasants(50, DagannothPeasants.class),
    SlayersSpite(51, SlayersSpite.class),
    HammerDown(52, HammerDown.class),
    AnimalTamer(53, AnimalTamer.class),
    FamiliarsFortune(54, FamiliarsFortune.class),
    NoPetDebt(55, NoPetDebt.class),
    CrystalCatalyst(56, CrystalCatalyst.class),
    LethalAttunement(57, LethalAttunement.class),
    RevItUp(58, RevItUp.class),
    SlayersSovereignty(59, SlayersSovereignty.class),
    SlayerPointBonus(60, SlayerPointBonus.class),
    DullAxes(61, DullAxes.class),
    EyeDontSeeYou(62, EyeDontSeeYou.class),
    AllGassedUp(63, AllGassedUp.class),
    BarrowsMazeMaster(64, BarrowsMazeMaster.class),
    CryptKeeper(65, CryptKeeper.class),
    GoldenLuck(66, GoldenLuck.class),
    DiamondLuck(67, DiamondLuck.class),
    EssenceMagnet(68, EssenceMagnet.class),
    IVotedII(69, IVotedII.class),
    IVotedIII(70, IVotedIII.class),
    MiningII(71, MiningII.class),
    MiningIII(72, MiningIII.class),
    MiningIV(73, MiningIV.class),
    MiningV(74, MiningV.class),
    WoodcuttingII(75, WoodcuttingII.class),
    WoodcuttingIII(76, WoodcuttingIII.class),
    WoodcuttingIV(77, WoodcuttingIV.class),
    WoodcuttingV(78, WoodcuttingV.class),
    FishingII(79, FishingII.class),
    FishingIII(80, FishingIII.class),
    FishingIV(81, FishingIV.class),
    FishingV(82, FishingV.class),


    /*
     * Special / fallback
     */
    Unknown(9999, UnknownEssencePerk.class);

    private final int id;
    private final Class<? extends EssencePerk> perk;
    private static final UtilityPerkWrapper[] VALUES = values();
    public static final Map<Class<? extends EssencePerk>, UtilityPerkWrapper> PERKS_BY_CLASS = new HashMap<>();
    private static final Map<String, UtilityPerkWrapper> PERKS_BY_NAME = new HashMap<>();
    public static final Int2ObjectOpenHashMap<UtilityPerkWrapper> PERKS_BY_ID = new Int2ObjectOpenHashMap<>();

    static {
        for (final UtilityPerkWrapper value : VALUES) {
            PERKS_BY_CLASS.put(value.getPerk(), value);
            PERKS_BY_NAME.put(value.name(), value);
            PERKS_BY_ID.put(value.getId(), value);
        }
    }

    static {
        Map<Integer, UtilityPerkWrapper> seen = new HashMap<>();
        for (UtilityPerkWrapper perk : values()) {
            if (seen.containsKey(perk.id)) {
                throw new IllegalStateException(
                        "Duplicate UtilityPerkWrapper ID " + perk.id +
                                " for " + perk.name() + " and " + seen.get(perk.id).name()
                );
            }
            seen.put(perk.id, perk);
        }
    }

    public static UtilityPerkWrapper getByClass(final Class<? extends EssencePerk> perk) {
        if(!PERKS_BY_CLASS.containsKey(perk))
            throw new RuntimeException("MISSING BOON WRAPPER: " + perk.getSimpleName());
        return PERKS_BY_CLASS.get(perk);
    }

    public static UtilityPerkWrapper getByString(final String perk) {
        return PERKS_BY_NAME.get(perk);
    }

    public static UtilityPerkWrapper getByClass(final int id) {
        return PERKS_BY_ID.getOrDefault(id, Unknown);
    }

    UtilityPerkWrapper(int id, Class<? extends EssencePerk> perk) {
        this.id = id;
        this.perk = perk;
    }

    public int getId() {
        return id;
    }

    public Class<? extends EssencePerk> getPerk() {
        return perk;
    }

}
