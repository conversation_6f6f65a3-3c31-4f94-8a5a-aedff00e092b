package com.zenyte.game.content.essence.utility.impl;

import com.zenyte.game.content.essence.EssencePerk;
import com.zenyte.game.content.essence.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> (Discord: Kryptexxical)
 */
public class MiningII extends EssencePerk {

    public static boolean roll() {
        return Utils.random(99) < 20;
    }
    public static boolean rollBank() { return Utils.random(99) < 30; }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_MiningII;
    }

    @Override
    public String name() {
        return "Mining (II)";
    }

    @Override
    public String description() {
        return "Adds a 30% chance to bank your ore when mining in addition to your 20% chance to double ores.";
    }

    @Override
    public boolean purchasable(Player p) {
        return p.hasUtilityPerk(MiningI.class);
    }

    @Override
    public String purchasableErrorMessage(Player player) {
        return "You must unlock the Mining (I) perk before you can purchase the Mining (II) perk.";
    }

    @Override
    public int item() {
        return 440;
    }

}
