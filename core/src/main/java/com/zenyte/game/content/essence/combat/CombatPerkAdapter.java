package com.zenyte.game.content.essence.combat;

import com.google.gson.*;
import com.zenyte.game.content.essence.EssencePerk;
import it.unimi.dsi.fastutil.objects.Object2ObjectOpenHashMap;

import java.lang.reflect.Type;

public class CombatPerkAdapter implements JsonSerializer<EssencePerk>, JsonDeserializer<EssencePerk> {
    private static final Object2ObjectOpenHashMap<String, Class<? extends EssencePerk>> REMAPPED_PERKS = new Object2ObjectOpenHashMap<>();

    static {
        //REMAPPED_PERKS.put("TumekensTribute", TumekensTribute.class);
    }


    @Override
    public JsonElement serialize(EssencePerk perk, Type typeOfSrc, JsonSerializationContext context) {
        final JsonObject result = new JsonObject();
        result.add("type", new JsonPrimitive(CombatPerkWrapper.getByClass(perk.getClass()).name()));
        result.add("properties", context.serialize(perk, perk.getClass()));
        return result;
    }

    @Override
    public EssencePerk deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        final String type = json.getAsJsonObject().get("type").getAsString();

        /*
         * Used to migrate perk names cleanly across changes (Keep it unlocked for player but change name..)
         * Must set alternateName for the perk in its class, and remap the perk above statically
         */
        if(REMAPPED_PERKS.containsKey(type))
            return context.deserialize(json, REMAPPED_PERKS.get(type));

        final CombatPerkWrapper perk = CombatPerkWrapper.getByString(type);
        if (perk != null) {
            return context.deserialize(json, perk.getPerk());
        }
        throw new IllegalArgumentException("Unknown perk: " + type);
    }
}