package com.zenyte.game.world.entity.player.cutscene.impl;

import com.zenyte.game.GameConstants;
import com.zenyte.game.model.HintArrow;
import com.zenyte.game.model.HintArrowPosition;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.cutscene.Cutscene;
import com.zenyte.game.world.entity.player.cutscene.actions.CameraLookAction;
import com.zenyte.game.world.entity.player.cutscene.actions.CameraResetAction;
import com.zenyte.game.world.entity.player.dialogue.impl.NPCChat;
import com.zenyte.plugins.renewednpc.ExilesGuide;

/**
 * <AUTHOR> | 26/01/2019 01:49
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class EdgevilleCutscene extends Cutscene {

    @Override
    public void build() {
        int time = 0;
        addActions(time+=1, () -> player.lock(), () -> player.setViewDistance(40),
                () -> player.getAppearance().setInvisible(true), () -> player.setLocation(new Location(3088, 3504)), () -> chat(player, "Great! This won't take long!"));

        addActions(time+=6, () -> action(player, "This is the Sigil Master where you can learn about Sigils and equip them!", 3090, 3492),
                new CameraLookAction(player, new Location(3090, 3492), 0, 5, 10));

        addActions(time+=6, () -> action(player, "This is the Lava Pool for sacrificing items for Essence, and the Perk Master for buying Combat Relics and Utility Perks!", 3089, 3508),
                new CameraLookAction(player, new Location(3089, 3508), 0, 5, 10));

        addActions(time+=6, () -> action(player, "The main shops are located to the North-West.", 3080, 3511),
                new CameraLookAction(player, new Location(3080, 3511), 0, 5, 10));

        addActions(time += 6, () -> action(player, "The Ironman Tutor can be found near the shops, for any Ironman related question and claiming armour.", 3081, 3504),
                new CameraLookAction(player, new Location(3081, 3504, 0), 0, 5, 10));

        addActions(time+=6, () -> action(player, "Currency shops and important NPCs can be found in the building to the North.", 3095, 3510),
                new CameraLookAction(player, new Location(3095, 3510, 0), 0, 5, 10));

        addActions(time+=6, () -> action(player, "All of the Slayer Masters are located to the North-East.", 3109, 3514),
                new CameraLookAction(player, new Location(3109, 3514, 0), 0, 5, 10));

        addActions(time+=10, () -> action(player, "All server wide events can be joined from this area to the South-West.", 3082, 3481),
                new CameraLookAction(player, new Location(3082, 3481, 0), 0, 5, 10));

        addActions(time+=10, () -> action(player, "Many skilling spots are located in the building to the East", 3108, 3499),
                new CameraLookAction(player, new Location(3108, 3499, 0), 0, 5, 10));

        addActions(time+=6, () -> action(player, "The main banking area and Grand Exchange can be found near the center of everything.", 3093, 3494),
                new CameraLookAction(player, new Location(3093, 3494, 0), 0, 5, 10));

        addActions(time+=6, () -> action(player, "Finally, this is the Teleportation Portal which you can use to teleport all around " + GameConstants.SERVER_NAME + "!", 3084, 3494),
                new CameraLookAction(player, new Location(3084, 3494, 0), 0, 5, 10), () -> player.setLocation(ExilesGuide.HOME_EXILES_GUIDE));

        addActions(time, () -> player.setViewDistance(15), () -> player.getPacketDispatcher().resetHintArrow(), () -> ExilesGuide.finishTutorial(player),
                new CameraResetAction(player));
    }

    private void action(final Player player, final String message, final int x, final int y) {
        player.getPacketDispatcher().sendHintArrow(new HintArrow(x, y, (byte) 50, HintArrowPosition.CENTER));
        chat(player, message);
    }

    private void chat(final Player player, final String message) {
        player.getDialogueManager().start(new NPCChat(player, ExilesGuide.NPC_ID, message, false));
    }
}
