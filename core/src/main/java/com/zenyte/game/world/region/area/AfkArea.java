package com.zenyte.game.world.region.area;

import com.near_reality.game.content.commands.DeveloperCommands;
import com.zenyte.game.content.afkzone.AfkZoneManager;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.privilege.PlayerPrivilege;
import com.zenyte.game.world.region.RSPolygon;
import com.zenyte.game.world.region.area.plugins.CannonRestrictionPlugin;
import com.zenyte.game.world.region.area.plugins.CycleProcessPlugin;
import com.zenyte.game.world.region.area.plugins.LoginPlugin;

import java.util.ArrayList;

public final class AfkArea extends EdgevilleArea implements CannonRestrictionPlugin, CycleProcessPlugin, LoginPlugin {

	public static final ArrayList<String> IP_ADDRESSES_IN_AREA = new ArrayList<>();
	private static final Location OUTSIDE = new Location(3112, 3483, 0);

	private static final int MAX_IPS_ALLOWED = 3;

	@Override
	public String name() {
		return "Afk zone";
	}

	@Override
	public RSPolygon[] polygons() {
		return new RSPolygon[] {new RSPolygon(new int[][] {
				{ 3112, 3480 },
				{ 3114, 3480 },
				{ 3117, 3483 },
				{ 3122, 3483 },
				{ 3125, 3480 },
				{ 3127, 3480 },
				{ 3127, 3463 },
				{ 3125, 3463 },
				{ 3122, 3460 },
				{ 3117, 3460 },
				{ 3114, 3463 },
				{ 3112, 3463 }})};
	}

	@Override
	public void enter(final Player player) {
		if(DeveloperCommands.INSTANCE.getIpBasedDetections()) {
			if (!canBypass(player) && IP_ADDRESSES_IN_AREA.stream().filter(it -> it.equalsIgnoreCase(player.getIP())).count() > MAX_IPS_ALLOWED) {
				player.sendMessage("You are only allowed to have three accounts in this area at a time.");
				player.setLocation(OUTSIDE);
				player.stopAll();
			}
			IP_ADDRESSES_IN_AREA.add(player.getIP());
		}
		player.sendMessage("Increased EXP from voting time remaining: " + Utils.formatDuration(AfkZoneManager.getAfkTime(player)));
	}

	@Override
	public void leave(final Player player, boolean logout) {
		if(DeveloperCommands.INSTANCE.getIpBasedDetections())
			IP_ADDRESSES_IN_AREA.remove(player.getIP());
	}

	@Override
	public void login(Player player) {
		if(DeveloperCommands.INSTANCE.getIpBasedDetections()) {
			if (!canBypass(player) && IP_ADDRESSES_IN_AREA.stream().filter(it -> it.equalsIgnoreCase(player.getIP())).count() > MAX_IPS_ALLOWED) {
				player.sendMessage("You are only allowed to have three accounts in this area at a time.");
				player.setLocation(OUTSIDE);
				player.stopAll();
			}
		}
	}

	@Override
	public void process() { }

	private boolean canBypass(Player player) {
		return player.getPrivilege().inherits(PlayerPrivilege.SUPPORT);
	}

}
