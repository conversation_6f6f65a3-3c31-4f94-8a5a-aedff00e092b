package com.zenyte.plugins.renewednpc;

import com.near_reality.api.service.user.UserPlayerHandler;
import com.near_reality.game.world.entity.player.PlayerAttributesKt;
import com.zenyte.ContentConstants;
import com.zenyte.game.GameConstants;
import com.zenyte.game.GameInterface;
import com.zenyte.game.content.achievementdiary.AdventurersLogIcon;
import com.zenyte.game.content.quests.quests.AdventurersPath;
import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.model.item.degradableitems.DegradableItem;
import com.zenyte.game.model.ui.InterfacePosition;
import com.zenyte.game.world.World;
import com.zenyte.game.world.broadcasts.BroadcastType;
import com.zenyte.game.world.broadcasts.WorldBroadcasts;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.npc.NpcId;
import com.zenyte.game.world.entity.npc.actions.NPCPlugin;
import com.zenyte.game.world.entity.player.LogLevel;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.cutscene.impl.EdgevilleCutscene;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.entity.player.privilege.ExpConfiguration;
import com.zenyte.game.world.entity.player.privilege.ExpConfigurations;
import com.zenyte.game.world.entity.player.privilege.GameMode;
import kotlin.Unit;
import org.jetbrains.annotations.NotNull;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR> | 25/11/2018 16:13
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class ExilesGuide extends NPCPlugin {

    public static final int NPC_ID = NpcId.EXILES_GUIDE;

    public static final Item[][] STARTER_ITEMS = {
            { // normal
                    new Item(995, 50000), new Item(841), new Item(882, 250), new Item(558, 250), new Item(556, 250), new Item(557, 250), new Item(555, 250), new Item(554, 250), new Item(1379), new Item(1323), new Item(1171), new Item(303), new Item(362, 50), new Item(1351), new Item(1265), new Item(22711), new Item(4548)},
            { // ironman
                    new Item(12810), new Item(12811), new Item(12812)
            },
            { //ult ironman
                    new Item(12813), new Item(12814), new Item(12815)},
            { //hc ironman
                    new Item(20794), new Item(20796)
            }, // group ironman
            {
                    new Item(ItemId.GROUP_IRON_HELM),
                    new Item(ItemId.GROUP_IRON_PLATEBODY),
                    new Item(ItemId.GROUP_IRON_PLATELEGS),
                    new Item(ItemId.GROUP_IRON_BRACERS),
            }, // hc group ironman
            {
                    new Item(ItemId.HARDCORE_GROUP_IRON_HELM),
                    new Item(ItemId.HARDCORE_GROUP_IRON_PLATEBODY),
                    new Item(ItemId.HARDCORE_GROUP_IRON_PLATELEGS),
                    new Item(ItemId.HARDCORE_GROUP_IRON_BRACERS),
            }
    };

    private static final Item EXTRA_TUTORIAL_GOLD = new Item(995, 50000);
    public static final Location HOME_EXILES_GUIDE = new Location(3086, 3493);
    public static final Location SPAWN_LOCATION = new Location(3086, 3493);
    public static boolean disableJoinAnnouncement = false;

    public static void finishAppearance(final Player player) {
        player.lock();
        player.getDialogueManager().start(new Dialogue(player, NPC_ID) {
            @Override
            public void buildDialogue() {
                npc("I've given you an assortment of starter weapons to try out. Hopefully this will help in your adventure on " + ContentConstants.SERVER_NAME + ".");
                giveWeapon(player, ItemId.STARTER_SWORD_28559);
                giveWeapon(player, ItemId.STARTER_BOW_28555);
                giveWeapon(player, ItemId.STARTER_STAFF_28557);
                npc("Would you like a quick tour?").executeAction(() -> {
                });
                //setting zoom depth
                options("Take a tour?", "Yes.", "No, thanks.").onOptionOne(() -> {
                    player.getPacketDispatcher().sendClientScript(10700, 1);
                    player.getPacketDispatcher().sendClientScript(42, 0, 200);
                    player.getCutsceneManager().play(new EdgevilleCutscene());
                }).onOptionTwo(() -> {
                    finishTutorial(player);
                });
            }
        });
    }

    private static void giveWeapon(@NotNull final Player player, final int weaponId) {
        final Item weapon = new Item(weaponId, 1, DegradableItem.getFullCharges(weaponId));
        player.getInventory().addOrDrop(weapon);
    }

    public static void finishTutorial(final Player player) {
        player.getAppearance().setInvisible(false);
        player.unlock();
        if (player.getBooleanTemporaryAttribute("tutorial_rewatch")) {
            player.setLocation(HOME_EXILES_GUIDE);
            player.getInterfaceHandler().closeInterface(InterfacePosition.DIALOGUE);
            player.addTemporaryAttribute("tutorial_rewatch", 0);
            return;
        }

        if (!disableJoinAnnouncement) {
            WorldBroadcasts.broadcast(player, BroadcastType.NEW_PLAYER);
        }
        Optional<NPC> optionalGuide = World.findNPC(NPC_ID, ExilesGuide.HOME_EXILES_GUIDE);
        optionalGuide.ifPresent(npc -> npc.setForceTalk("Welcome " + player.getName() + " to " + ContentConstants.SERVER_NAME + "!"));

        //Fixed: The selected game mode variable is now loaded during login in Player.java if tutorial hasn't been completed.
        final GameMode gameMode = PlayerAttributesKt.getSelectedGameMode(player);
        UserPlayerHandler.INSTANCE.updateGameMode(player, gameMode, (success) -> {
            if (!success) {
                player.log(LogLevel.ERROR, "Failed to update game-mode " + gameMode + " in API, setting anyways.");
                player.setGameMode(gameMode);
            }
            for (final Item item : STARTER_ITEMS[0]) {
                final Item it = new Item(item.getId(), item.getAmount(), DegradableItem.getDefaultCharges(item.getId(), 0));
                player.getInventory().addItem(it);
            }
            if (player.isIronman()) {
                final Item[] items = STARTER_ITEMS[player.getGameMode().ordinal()];
                for (final Item item : items) {
                    final Item it = new Item(item.getId(), item.getAmount(), DegradableItem.getDefaultCharges(item.getId(), 0));
                    try {
                        Class<?> ironmanGroupClass = Class.forName("com.near_reality.content.group_ironman.IronmanGroup");
                        Object group = ironmanGroupClass.getMethod("find", com.zenyte.game.world.entity.player.Player.class).invoke(null, player);

                        if (player.isGroupIronman() && group == null) {
                            player.sendMessage("You must be in a group to claim a set of armour.");
                            break;
                        }
                        @SuppressWarnings("unchecked")
                        java.util.List<Object> members = (java.util.List<Object>) group.getClass().getMethod("getActiveMembersForJava").invoke(group);
                        int groupSize = members.size();
                        if (it.getId() == ItemId.GROUP_IRON_PLATEBODY) {
                            switch (groupSize) {
                                case 2:
                                    player.getInventory().addItem(new Item(ItemId.GROUP_IRON_PLATEBODY));
                                    break;
                                case 3:
                                    player.getInventory().addItem(new Item(ItemId.GROUP_IRON_PLATEBODY_26160));
                                    break;
                                case 4:
                                    player.getInventory().addItem(new Item(ItemId.GROUP_IRON_PLATEBODY_26162));
                                    break;
                                case 5:
                                    player.getInventory().addItem(new Item(ItemId.GROUP_IRON_PLATEBODY_26164));
                                    break;
                                default:
                                    player.sendMessage("Group size is invalid for claiming platebody.");
                                    break;
                            }
                        } else {
                            player.getInventory().addItem(it);
                        }
                    } catch (Exception e) {
                        player.sendMessage("Group ironman system is not available.");
                        player.getInventory().addItem(it);
                    }
                }
            }
//            if(!player.getBooleanAttribute("claimedFounders") && Calendar.getInstance().get(Calendar.YEAR) == 2024 && Calendar.getInstance().get(Calendar.MONTH) == Calendar.MARCH && Calendar.getInstance().get(Calendar.DAY_OF_MONTH) <  19) {
//                player.sendMessage(Colour.RS_GREEN.wrap("Thank you for joining " + ContentConstants.SERVER_NAME + " on our launch weekend!"));
//                player.sendMessage(Colour.RS_GREEN.wrap("The powerful Founder's Cape has been added to your inventory."));
//                player.getInventory().addItem(new Item(CustomItemId.FOUNDERS_CAPE));
//                player.putBooleanAttribute("claimedFounders", true);
//            }
            player.getTemporaryAttributes().remove("registration");
            player.putBooleanAttribute("registered", true);
            player.putBooleanAttribute("in_tutorial", false);
            player.getTaskManager().assignRandomChallenge(2);
            AdventurersPath.startQuest(player);
            return Unit.INSTANCE;
        });
    }

    /**
     * Handles the new player registration dialogue and game mode selection.
     * This method can be called from other classes for new players.
     *
     * @param player The player to handle
     * @param npc The NPC (can be null if called from other contexts)
     */
    public static void handleNewPlayerDialogue(final Player player, final NPC npc) {
        player.stopAll();
        if (npc != null) {
            player.faceEntity(npc);
        }

        player.getDialogueManager().start(new Dialogue(player, npc) {
            @Override
            public void buildDialogue() {
                npc("Welcome to " + GameConstants.SERVER_NAME + "!");
                npc("Please select a game mode.").executeAction(() -> {
                    player.getTemporaryAttributes().put("ironman_setup", "register");
                    player.getVarManager().sendVar(281, 0);
                    player.getVarManager().sendBit(1777, 0);
                    player.setExperienceMultiplier(300, 50);
                    GameInterface.GAME_MODE_SETUP.open(player);
                });
            }
        });
    }

    /**
     * Handles the registered player dialogue with experience mode options and tutorial rewatch.
     * This method can be called from other classes for existing players.
     *
     * @param player The player to handle
     * @param npc The NPC (can be null if called from other contexts)
     */
    public static void handleRegisteredPlayerDialogue(final Player player, final NPC npc) {
        player.stopAll();
        if (npc != null) {
            player.faceEntity(npc);
        }

        player.getDialogueManager().start(new Dialogue(player, npc) {
            @Override
            public void buildDialogue() {
                npc("Hey! It's good to see you again, " + player.getName() + ". What can I do for you?");
                options(TITLE, "Talk about my experience mode.", "Rewatch the tutorial.", "Do you have any quests?").onOptionOne(() -> {
                    setKey(5);
                }).onOptionTwo(() -> {
                    player.lock();
                    player.getPacketDispatcher().sendClientScript(10700, 1);
                    //player.getPacketDispatcher().sendClientScript(42, 0, 200); //setting zoom depth
                    //player.setLocation(SPAWN_LOCATION);
                    player.addTemporaryAttribute("tutorial_rewatch", 1);
                    player.getCutsceneManager().play(new EdgevilleCutscene());
                }).onOptionThree(() -> {
                    AdventurersPath.startQuest(player);
                });
                ExpConfigurations config = ExpConfigurations.of(player.getGameMode());
                if (config != null) {
                    int currentIndex = config.getExpConfigurationIndex(player.getCombatXPRate(), player.getSkillingXPRate());
                    ExpConfiguration currentExp = config.getConfigurations()[currentIndex];
                    ExpConfiguration[] easier = config.getEasier(currentIndex);
                    if (easier != null) {
                        String[] options = Arrays.stream(easier).map(ExpConfiguration::getString).toList().toArray(new String[0]);
                        npc(5, "I see that you're on the <col=00080>" + currentExp.getString() + " mode</col>. Would you be interested in changing that into an easier experience mode?");
                        options("Select the experience mode", options)
                                .onOptionOne(() -> setKey(selectConfigurationMode(player, easier, 0)))
                                .onOptionTwo(() -> setKey(selectConfigurationMode(player, easier, 1)))
                                .onOptionThree(() -> setKey(selectConfigurationMode(player, easier, 2)))
                                .onOptionFour(() -> setKey(selectConfigurationMode(player, easier, 3)))
                                .onOptionFive(() -> setKey(selectConfigurationMode(player, easier, 4)));
                    } else {
                        npc(5, "There are no easier modes available for you.");
                    }
                } else {
                    npc(5, "There are no easier modes available for you.");
                }
                npc(35, "Please be aware you will not be able to revert this change if you accept.").executeAction(() -> confirmDialogue(player, npc));
            }
        });
    }

    /**
     * Main dialogue handler that determines which dialogue to show based on player registration status.
     * This method can be called from other classes to trigger the appropriate dialogue.
     *
     * @param player The player to handle
     * @param npc The NPC (can be null if called from other contexts)
     */
    public static void handlePlayerDialogue(final Player player, final NPC npc) {
        if (!player.getBooleanAttribute("registered")) {
            handleNewPlayerDialogue(player, npc);
        } else {
            handleRegisteredPlayerDialogue(player, npc);
        }
    }

    @Override
    public void handle() {
        bind("Talk-to", new OptionHandler() {
            @Override
            public void handle(Player player, NPC npc) {
                player.stopAll();
                player.faceEntity(npc);
                handlePlayerDialogue(player, npc);
            }

            @Override
            public void execute(final Player player, final NPC npc) {
                player.stopAll();
                player.setFaceEntity(npc);
                handle(player, npc);
                if (npc.getLocation().getPositionHash() != HOME_EXILES_GUIDE.getPositionHash()) {
                    npc.setInteractingWith(player);
                }
            }
        });
    }

    private static void confirmDialogue(Player player, NPC npc) {
        player.getDialogueManager().start(new Dialogue(player, npc) {
            @Override
            public void buildDialogue() {
                options("Choose " + (((ExpConfiguration) player.getTemporaryAttributes().get("review_exp")).getString()) + "?",
                        "Yes please", "No thanks").onOptionOne(() -> {
                    setMode(player, (ExpConfiguration) player.getTemporaryAttributes().get("review_exp"));
                    showNewMode(player, npc);
                }).onOptionTwo(() -> setKey(15));
                npc(15, "That's fine, come back if you change your mind!");
            }
        });
    }

    private static void showNewMode(Player player, NPC npc) {
        player.getDialogueManager().start(new Dialogue(player, npc) {
            @Override
            public void buildDialogue() {
                npc("Your new exp mode is " + player.getCombatXPRate() + "x combat, " + player.getSkillingXPRate() + "x skilling.");
            }
        });
    }

    private static int selectConfigurationMode(Player player, ExpConfiguration[] easier, int index) {
        if (index >= easier.length) {
            return 25;
        } else {
            player.addTemporaryAttribute("review_exp", easier[index]);
            return 35;
        }
    }


    private static void setMode(Player player, ExpConfiguration expConfiguration) {
        player.setExperienceMultiplier(expConfiguration);
        player.sendAdventurersEntry(AdventurersLogIcon.OVERALL_SKILLING, player.getName() + " has just changed exp mode - they are now playing under the " + expConfiguration + " mode!");
    }

    private static final Item COST = new Item(995, 50_000);

    private static void purchase(@NotNull final Player player, @NotNull final NPC npc, final int weaponId) {
        player.getDialogueManager().finish();
        player.getDialogueManager().start(new Dialogue(player, npc) {
            @Override
            public void buildDialogue() {
                if (!player.getInventory().containsItem(COST)) {
                    npc("You need at least 50,000 coins to purchase it!");
                    return;
                }
                if (!player.getInventory().hasFreeSlots()) {
                    npc("Perhaps you should make some free space in your inventory first.");
                    return;
                }
                player.getInventory().deleteItem(COST);
                final Item weapon = new Item(weaponId, 1, DegradableItem.getFullCharges(weaponId));
                player.getInventory().addOrDrop(weapon);
                item(weapon, "The guide hands you a " + weapon.getName().toLowerCase() + ".");
                npc("Pleasure doing business with you.");
            }
        });
    }

    @Override
    public int[] getNPCs() {
        return new int[]{NPC_ID};
    }
}
