package com.zenyte.game.content.hiscores;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify the entry counts for each hiscores category.
 */
public class CategoryCountTest {

    @Test
    public void testCategoryCounts() {
        System.out.println("Hiscores Category Entry Counts:");
        
        int totalEntries = 0;
        int currentSlot = 0;
        
        for (HiscoresCategory category : HiscoresCategory.values) {
            int entryCount = category.getEntries().length;
            System.out.printf("%s (%d): %d entries - slots %d-%d%n", 
                category.name(), category.getStructId(), entryCount, 
                currentSlot, currentSlot + entryCount - 1);
            
            totalEntries += entryCount;
            currentSlot += entryCount;
        }
        
        System.out.printf("Total entries across all categories: %d%n", totalEntries);
        System.out.printf("Valid slot range: 0-%d%n", totalEntries - 1);
        
        // Test specific slot mappings
        testSlotMapping(0, "SKILLING", 0);
        testSlotMapping(22, "SKILLING", 22);
        testSlotMapping(23, "BOSSES", 0);
        testSlotMapping(81, "should be out of bounds or in a specific category", -1);
    }
    
    private void testSlotMapping(int slotId, String expectedCategory, int expectedPosition) {
        int currentSlot = 0;
        
        for (HiscoresCategory category : HiscoresCategory.values) {
            int categorySize = category.getEntries().length;
            
            if (slotId >= currentSlot && slotId < currentSlot + categorySize) {
                int positionInCategory = slotId - currentSlot;
                System.out.printf("Slot %d maps to %s category, position %d%n", 
                    slotId, category.name(), positionInCategory);
                
                if (!expectedCategory.contains("out of bounds")) {
                    assertEquals(expectedCategory, category.name(), 
                        String.format("Slot %d should map to %s category", slotId, expectedCategory));
                    if (expectedPosition >= 0) {
                        assertEquals(expectedPosition, positionInCategory, 
                            String.format("Slot %d should map to position %d", slotId, expectedPosition));
                    }
                }
                return;
            }
            
            currentSlot += categorySize;
        }
        
        System.out.printf("Slot %d is out of bounds%n", slotId);
        if (expectedCategory.contains("out of bounds")) {
            // This is expected
        } else {
            fail(String.format("Slot %d should be valid but was not found", slotId));
        }
    }
}
